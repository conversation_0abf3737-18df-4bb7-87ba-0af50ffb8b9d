import React, { useState } from 'react';
import { Search, BookOpen, ArrowRight } from 'lucide-react';
import pagesData from '../data/pages.json';

interface PagesListProps {
  onPageSelect: (pageNumber: number) => void;
  currentPage?: number;
  onBack?: () => void;
}

interface PageData {
  page: number;
  juz: number;
  surahs: Array<{
    surah: number;
    name: string;
    verses: number[];
  }>;
}

const PagesList: React.FC<PagesListProps> = ({ onPageSelect, currentPage = 1, onBack }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedJuz, setSelectedJuz] = useState<number | null>(null);

  // فلترة الصفحات بناءً على البحث والجزء المحدد
  const filteredPages = pagesData.filter((page: PageData) => {
    const matchesSearch = searchTerm === '' || 
      page.page.toString().includes(searchTerm) ||
      page.surahs.some(surah => surah.name.includes(searchTerm));
    
    const matchesJuz = selectedJuz === null || page.juz === selectedJuz;
    
    return matchesSearch && matchesJuz;
  });

  // الحصول على قائمة الأجزاء المتاحة
  const availableJuz = Array.from(new Set(pagesData.map((page: PageData) => page.juz))).sort((a, b) => a - b);

  const handlePageClick = (pageNumber: number) => {
    onPageSelect(pageNumber);
  };

  const getPageInfo = (page: PageData) => {
    if (page.surahs.length === 1) {
      const surah = page.surahs[0];
      return `سورة ${surah.name}`;
    } else {
      return `${page.surahs.length} سور`;
    }
  };

  return (
    <div className="bg-gray-900 min-h-screen">
      {/* Header */}
      <div className="bg-gray-800 px-4 py-3 border-b border-gray-700">
        <div className="flex items-center justify-between">
          {onBack && (
            <button
              onClick={onBack}
              className="p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors"
            >
              <ArrowRight className="w-5 h-5 text-white" />
            </button>
          )}
          <h2 className="text-xl font-bold text-white">الصفحات</h2>
          <div className="w-9"></div> {/* Spacer for centering */}
        </div>
      </div>

      {/* Search and Filter */}
      <div className="p-4 space-y-4">
        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="ابحث عن صفحة أو سورة..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-gray-800 text-white rounded-lg pl-4 pr-12 py-3 border border-gray-700 focus:border-green-500 focus:outline-none"
          />
        </div>

        {/* Juz Filter */}
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedJuz(null)}
            className={`px-3 py-1 rounded-full text-sm ${
              selectedJuz === null
                ? 'bg-green-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            كل الأجزاء
          </button>
          {availableJuz.map((juz) => (
            <button
              key={juz}
              onClick={() => setSelectedJuz(juz)}
              className={`px-3 py-1 rounded-full text-sm ${
                selectedJuz === juz
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              الجزء {juz}
            </button>
          ))}
        </div>
      </div>

      {/* Pages Grid */}
      <div className="px-4 pb-4">
        {filteredPages.length > 0 ? (
          <div className="grid grid-cols-2 gap-3">
            {filteredPages.map((page: PageData) => (
              <button
                key={page.page}
                onClick={() => handlePageClick(page.page)}
                className={`p-4 rounded-lg border transition-all duration-200 text-right ${
                  currentPage === page.page
                    ? 'bg-green-600 border-green-500 text-white shadow-lg'
                    : 'bg-gray-800 border-gray-700 text-white hover:bg-gray-700 hover:border-gray-600'
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <BookOpen className="w-5 h-5 text-green-400 mt-1" />
                  <div className="text-right">
                    <div className="text-lg font-bold">الصفحة {page.page}</div>
                    <div className="text-sm text-gray-400">الجزء {page.juz}</div>
                  </div>
                </div>
                
                <div className="text-sm text-gray-300 mt-2">
                  {getPageInfo(page)}
                </div>
                
                {/* عرض أسماء السور إذا كان هناك أكثر من سورة */}
                {page.surahs.length > 1 && (
                  <div className="text-xs text-gray-400 mt-1">
                    {page.surahs.map(surah => surah.name).join(' • ')}
                  </div>
                )}
              </button>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-400 py-8">
            <BookOpen className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>لم يتم العثور على صفحات</p>
            <p className="text-sm mt-2">جرب تغيير مصطلح البحث أو الفلتر</p>
          </div>
        )}
      </div>

      {/* Quick Navigation */}
      <div className="bg-gray-800 border-t border-gray-700 p-4">
        <div className="text-center text-sm text-gray-400 mb-3">
          الانتقال السريع
        </div>
        <div className="grid grid-cols-5 gap-2">
          {[1, 50, 100, 200, 300, 400, 500, 550, 580, 604].map((pageNum) => (
            <button
              key={pageNum}
              onClick={() => handlePageClick(pageNum)}
              className="py-2 px-3 bg-gray-700 hover:bg-gray-600 rounded-lg text-sm text-white transition-colors"
            >
              {pageNum}
            </button>
          ))}
        </div>
      </div>

      {/* Stats */}
      <div className="bg-gray-800 border-t border-gray-700 p-4 text-center">
        <div className="text-sm text-gray-400">
          إجمالي الصفحات: {pagesData.length} • 
          الصفحات المعروضة: {filteredPages.length}
        </div>
      </div>
    </div>
  );
};

export default PagesList;
