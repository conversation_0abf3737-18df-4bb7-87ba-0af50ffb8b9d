import React from 'react';
import { useSwipeable } from 'react-swipeable';

interface QuranPageViewerProps {
  pageNumber: number;
  onBack: () => void;
  onPageChange: (newPage: number) => void;
  totalPages?: number;
  surahName?: string;
  juzNumber?: number;
}

const QuranPageViewer: React.FC<QuranPageViewerProps> = ({
  pageNumber,
  onBack,
  onPageChange,
  totalPages = 604,
  surahName,
  juzNumber
}) => {
  // اسم الصورة بناءً على رقم الصفحة
  const paddedPage = pageNumber.toString().padStart(4, '0');
  const imageUrl = `/images_cropped1/${paddedPage}.jpg`;

  // Swipe handlers
  const handlers = useSwipeable({
    onSwipedLeft: () => pageNumber < totalPages && onPageChange(pageNumber + 1),
    onSwipedRight: () => pageNumber > 1 && onPageChange(pageNumber - 1),
    trackMouse: true,
  });

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-[#f9f7f1] relative select-none overflow-hidden">
      {/* زر الرجوع */}
      <button
        onClick={onBack}
        className="fixed top-4 right-4 bg-white/80 hover:bg-white/100 text-gray-800 p-2 rounded-full shadow-lg z-20 transition-all border border-gray-200"
        style={{ width: 44, height: 44, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        aria-label="رجوع"
      >
        <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="15 18 9 12 15 6"></polyline></svg>
      </button>

      {/* رأس السورة وأعلى الصفحة */}
      <div className="absolute top-0 left-0 w-full flex flex-col items-center pt-6 z-10 pointer-events-none">
        <div className="flex flex-row justify-between w-full px-8 mb-2">
          <span className="text-gray-500 text-lg font-arabic">{juzNumber ? `الجزء ${juzNumber}` : ''}</span>
          <span className="text-gray-500 text-lg font-arabic">{surahName || ''}</span>
        </div>
      </div>

      {/* إطار مزخرف حول صورة الصفحة */}
      <div className="quran-page-original relative flex items-center justify-center w-full h-full" style={{ minHeight: '80vh' }}>
        {/* صورة الإطار */}
        <div
          className="absolute inset-0 w-full h-full bg-cover bg-center bg-no-repeat opacity-40 pointer-events-none"
          style={{
            backgroundImage: `url(/assets/images/islamic-page-border.jpg)`,
            backgroundSize: '100% 100%',
            zIndex: 1,
          }}
        />
        {/* صورة الصفحة */}
        <div {...handlers} className="relative z-10 flex items-center justify-center w-full h-full" style={{ minHeight: '80vh' }}>
          <img
            src={imageUrl}
            alt={`صفحة ${pageNumber}`}
            className="select-none shadow-xl"
            style={{
              maxWidth: '90vw',
              maxHeight: '80vh',
              width: 'auto',
              height: 'auto',
              imageRendering: 'auto',
              display: 'block',
              margin: 'auto',
              borderRadius: 24,
              background: 'white',
            }}
            draggable={false}
          />
        </div>
      </div>

      {/* رقم الصفحة في الأسفل */}
      <div className="absolute bottom-6 left-0 w-full flex justify-center items-center z-10 pointer-events-none">
        <div className="inline-flex items-center justify-center px-6 py-2 bg-gradient-to-r from-amber-100 to-yellow-100 rounded-full border-2 border-amber-300 shadow-lg">
          <span className="text-xl font-bold text-amber-800 font-arabic">{pageNumber}</span>
        </div>
      </div>
    </div>
  );
};

export default QuranPageViewer; 