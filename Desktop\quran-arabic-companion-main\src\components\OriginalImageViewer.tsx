import React, { useState, useEffect } from 'react';
import '../styles/original-image-viewer.css';

interface OriginalImageViewerProps {
  page: number;
  darkMode: boolean;
  onPageChange: (page: number) => void;
}

const TOTAL_PAGES = 604;

const OriginalImageViewer: React.FC<OriginalImageViewerProps> = ({ 
  page, 
  darkMode, 
  onPageChange 
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // تنسيق رقم الصفحة ليكون 4 أرقام مع أصفار في البداية
  const formatPageNumber = (pageNum: number): string => {
    return pageNum.toString().padStart(4, '0');
  };

  const imagePath = `/images/${formatPageNumber(page)}.jpg`;

  useEffect(() => {
    setImageLoaded(false);
    setImageError(false);
  }, [page]);

  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageLoaded(false);
    setImageError(true);
  };

  const goToPage = (newPage: number) => {
    if (newPage >= 1 && newPage <= TOTAL_PAGES) {
      onPageChange(newPage);
    }
  };

  const goToPreviousPage = () => {
    if (page > 1) {
      goToPage(page - 1);
    }
  };

  const goToNextPage = () => {
    if (page < TOTAL_PAGES) {
      goToPage(page + 1);
    }
  };

  // التنقل بالكيبورد
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
        event.preventDefault();
        if (event.key === 'ArrowLeft') {
          goToNextPage(); // في العربية، السهم الأيسر يذهب للصفحة التالية
        } else {
          goToPreviousPage(); // السهم الأيمن يذهب للصفحة السابقة
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => {
      window.removeEventListener('keydown', handleKeyPress);
    };
  }, [page]);

  return (
    <div className={`original-image-viewer ${darkMode ? 'dark' : ''}`}>
      <div className="image-container">
        {!imageLoaded && !imageError && (
          <div className="loading-spinner">
            <div className="spinner"></div>
            <p>جاري تحميل الصفحة...</p>
          </div>
        )}
        
        {imageError && (
          <div className="error-message">
            <p>عذراً، لا يمكن تحميل صورة الصفحة {page}</p>
            <p>تأكد من وجود الملف: {imagePath}</p>
          </div>
        )}

        <img
          src={imagePath}
          alt={`صفحة ${page} من المصحف الشريف`}
          className={`quran-page-image ${imageLoaded ? 'loaded' : ''}`}
          onLoad={handleImageLoad}
          onError={handleImageError}
          style={{ display: imageError ? 'none' : 'block' }}
        />
      </div>

      <div className="image-navigation">
        <button 
          onClick={goToPreviousPage} 
          disabled={page === 1}
          className="nav-button prev-button"
          title="الصفحة السابقة (→)"
        >
          <span className="nav-icon">→</span>
          <span className="nav-text">السابقة</span>
        </button>

        <div className="page-info">
          <span className="page-number">صفحة {page}</span>
          <span className="total-pages">من {TOTAL_PAGES}</span>
        </div>

        <button 
          onClick={goToNextPage} 
          disabled={page === TOTAL_PAGES}
          className="nav-button next-button"
          title="الصفحة التالية (←)"
        >
          <span className="nav-text">التالية</span>
          <span className="nav-icon">←</span>
        </button>
      </div>

      <div className="keyboard-hint">
        <p>استخدم أسهم الكيبورد للتنقل بين الصفحات</p>
      </div>
    </div>
  );
};

export default OriginalImageViewer;
