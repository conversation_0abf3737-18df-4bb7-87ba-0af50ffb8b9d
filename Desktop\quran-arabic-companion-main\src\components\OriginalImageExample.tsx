import React from 'react';

// مثال لاستخدام الصور الأصلية التي أرسلتها
const OriginalImageExample: React.FC = () => {
  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-8">
      
      {/* مثال 1: استخدام الصورة الأولى (الإطار المقوس) لرأس السورة */}
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">مثال 1: رأس السورة بالإطار الذهبي</h2>
        
        <div className="relative inline-block">
          {/* ضع هنا رابط الصورة الأولى التي أرسلتها */}
          <img
            src="/assets/images/islamic-frame-1.jpg"
            alt="Islamic Frame"
            className="w-96 h-auto"
          />
          
          {/* النص فوق الصورة */}
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <h3 className="text-3xl font-bold text-gray-800 mb-2">الفاتحة</h3>
            <p className="text-lg text-gray-600">Al-Fatiha • 1</p>
          </div>
        </div>
      </div>

      {/* مثال 2: استخدام الصورة الثانية (تصميم الصفحات) كإطار للمحتوى */}
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">مثال 2: تخطيط الصفحة بالإطار التقليدي</h2>
        
        <div className="relative max-w-4xl mx-auto">
          {/* ضع هنا رابط الصورة الثانية التي أرسلتها */}
          <img
            src="/assets/images/islamic-page-border.jpg"
            alt="Islamic Page Border"
            className="w-full h-auto"
          />
          
          {/* المحتوى داخل الإطار */}
          <div className="absolute inset-0 flex items-center justify-center p-16">
            <div className="text-center space-y-6">
              {/* البسملة */}
              <div className="mb-8">
                <p className="text-2xl font-arabic text-gray-800">
                  بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
                </p>
              </div>
              
              {/* الآيات */}
              <div className="space-y-4">
                <p className="text-xl font-arabic text-right leading-loose">
                  الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ ﴿١﴾
                </p>
                <p className="text-xl font-arabic text-right leading-loose">
                  الرَّحْمَٰنِ الرَّحِيمِ ﴿٢﴾
                </p>
                <p className="text-xl font-arabic text-right leading-loose">
                  مَالِكِ يَوْمِ الدِّينِ ﴿٣﴾
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* مثال 3: استخدام الصور كخلفيات CSS */}
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">مثال 3: استخدام كخلفية CSS</h2>
        
        <div 
          className="relative w-full max-w-2xl mx-auto h-64 bg-cover bg-center bg-no-repeat rounded-lg shadow-lg flex items-center justify-center"
          style={{
            backgroundImage: 'url(/assets/images/islamic-frame-1.jpg)',
            backgroundSize: 'contain'
          }}
        >
          <div className="text-center text-white drop-shadow-2xl">
            <h3 className="text-4xl font-bold mb-2">البقرة</h3>
            <p className="text-xl">Al-Baqarah • 2</p>
          </div>
        </div>
      </div>

      {/* مثال 4: دمج الصورتين معاً */}
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">مثال 4: دمج الصورتين</h2>
        
        <div className="space-y-6">
          {/* رأس السورة */}
          <div className="relative inline-block">
            <img
              src="/assets/images/islamic-frame-1.jpg"
              alt="Surah Header"
              className="w-80 h-auto"
            />
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <h3 className="text-2xl font-bold text-gray-800">آل عمران</h3>
              <p className="text-sm text-gray-600">Al-Imran • 3</p>
            </div>
          </div>
          
          {/* محتوى الصفحة */}
          <div className="relative max-w-3xl mx-auto">
            <img
              src="/assets/images/islamic-page-border.jpg"
              alt="Page Content"
              className="w-full h-auto opacity-90"
            />
            <div className="absolute inset-0 flex items-center justify-center p-12">
              <div className="text-center">
                <p className="text-lg font-arabic text-gray-800 leading-relaxed">
                  الم ﴿١﴾ اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ ﴿٢﴾
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* تعليمات للمطور */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-8">
        <h3 className="text-xl font-bold text-blue-800 mb-4">📝 تعليمات الاستخدام:</h3>
        <div className="space-y-2 text-blue-700">
          <p><strong>1.</strong> احفظ الصورتين في مجلد: <code>public/assets/images/</code></p>
          <p><strong>2.</strong> أعد تسمية الصور إلى:</p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li><code>islamic-frame-1.jpg</code> (الإطار المقوس)</li>
            <li><code>islamic-page-border.jpg</code> (إطار الصفحة)</li>
          </ul>
          <p><strong>3.</strong> استخدم المكونات الجاهزة أعلاه</p>
          <p><strong>4.</strong> يمكنك تعديل الأحجام والألوان حسب الحاجة</p>
        </div>
      </div>
    </div>
  );
};

export default OriginalImageExample;
