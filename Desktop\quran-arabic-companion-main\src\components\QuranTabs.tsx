import { useState } from "react";

interface QuranTabsProps {
  activeTab: 'suras' | 'ajza' | 'pages';
  onTabChange: (tab: 'suras' | 'ajza' | 'pages') => void;
}

const QuranTabs = ({ activeTab, onTabChange }: QuranTabsProps) => {
  return (
    <div className="bg-gray-900 p-4">
      <div className="flex bg-gray-800 rounded-full p-1">
        <button
          onClick={() => onTabChange('suras')}
          className={`flex-1 py-2 px-4 rounded-full text-sm font-medium transition-colors ${
            activeTab === 'suras'
              ? 'bg-green-600 text-white'
              : 'text-gray-400 hover:text-white'
          }`}
        >
          السور
        </button>
        <button
          onClick={() => onTabChange('ajza')}
          className={`flex-1 py-2 px-4 rounded-full text-sm font-medium transition-colors ${
            activeTab === 'ajza'
              ? 'bg-green-600 text-white'
              : 'text-gray-400 hover:text-white'
          }`}
        >
          الأجزاء
        </button>
        <button
          onClick={() => onTabChange('pages')}
          className={`flex-1 py-2 px-4 rounded-full text-sm font-medium transition-colors ${
            activeTab === 'pages'
              ? 'bg-green-600 text-white'
              : 'text-gray-400 hover:text-white'
          }`}
        >
          الصفحات
        </button>
      </div>
    </div>
  );
};

export default QuranTabs;
