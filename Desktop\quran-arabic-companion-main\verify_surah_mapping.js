import fs from 'fs';

console.log('🔍 التحقق من صحة ربط السور بالمحتوى...\n');

// قراءة بيانات السور
const surahData = JSON.parse(fs.readFileSync('./src/data/surah.json', 'utf8'));

console.log('📋 فحص السور الأساسية:');

// فحص السور المهمة
const importantSurahs = [
  { id: 1, name: 'الفاتحة', expectedVerses: 7 },
  { id: 2, name: 'البقرة', expectedVerses: 286 },
  { id: 3, name: 'آل عمران', expectedVerses: 200 },
  { id: 18, name: 'الكهف', expectedVerses: 110 },
  { id: 36, name: 'يس', expectedVerses: 83 },
  { id: 67, name: 'الملك', expectedVerses: 30 },
  { id: 112, name: 'الإخلاص', expectedVerses: 4 },
  { id: 113, name: 'الفلق', expectedVerses: 5 },
  { id: 114, name: 'الناس', expectedVerses: 6 }
];

let allCorrect = true;

for (const surah of importantSurahs) {
  try {
    // قراءة ملف السورة
    const surahFile = `./src/data/surah/surah_${surah.id}.json`;
    const surahContent = JSON.parse(fs.readFileSync(surahFile, 'utf8'));
    
    // التحقق من الفهرس
    const actualIndex = parseInt(surahContent.index);
    if (actualIndex !== surah.id) {
      console.log(`❌ السورة ${surah.id} (${surah.name}): فهرس خاطئ - متوقع ${surah.id}، موجود ${actualIndex}`);
      allCorrect = false;
      continue;
    }
    
    // التحقق من عدد الآيات
    const actualVerses = Object.keys(surahContent.verse).length;
    // طرح البسملة إذا كانت موجودة
    const actualVersesCount = surahContent.verse.verse_0 ? actualVerses - 1 : actualVerses;
    
    if (actualVersesCount !== surah.expectedVerses) {
      console.log(`❌ السورة ${surah.id} (${surah.name}): عدد آيات خاطئ - متوقع ${surah.expectedVerses}، موجود ${actualVersesCount}`);
      allCorrect = false;
      continue;
    }
    
    // التحقق من وجود الآيات
    let missingVerses = [];
    for (let i = 1; i <= surah.expectedVerses; i++) {
      if (!surahContent.verse[`verse_${i}`]) {
        missingVerses.push(i);
      }
    }
    
    if (missingVerses.length > 0) {
      console.log(`❌ السورة ${surah.id} (${surah.name}): آيات مفقودة - ${missingVerses.join(', ')}`);
      allCorrect = false;
      continue;
    }
    
    // التحقق من محتوى الآية الأولى والأخيرة
    const firstVerse = surahContent.verse.verse_1;
    const lastVerse = surahContent.verse[`verse_${surah.expectedVerses}`];
    
    if (!firstVerse || firstVerse.trim() === '') {
      console.log(`❌ السورة ${surah.id} (${surah.name}): الآية الأولى فارغة`);
      allCorrect = false;
      continue;
    }
    
    if (!lastVerse || lastVerse.trim() === '') {
      console.log(`❌ السورة ${surah.id} (${surah.name}): الآية الأخيرة فارغة`);
      allCorrect = false;
      continue;
    }
    
    console.log(`✅ السورة ${surah.id} (${surah.name}): صحيحة - ${actualVersesCount} آية`);
    
  } catch (error) {
    console.log(`❌ السورة ${surah.id} (${surah.name}): خطأ في القراءة - ${error.message}`);
    allCorrect = false;
  }
}

console.log('\n🔍 فحص خاص لسورة الناس:');
try {
  const surahNas = JSON.parse(fs.readFileSync('./src/data/surah/surah_114.json', 'utf8'));
  console.log(`📖 محتوى سورة الناس:`);
  console.log(`   - الفهرس: ${surahNas.index}`);
  console.log(`   - الاسم: ${surahNas.name}`);
  console.log(`   - عدد الآيات: ${surahNas.count}`);
  console.log(`   - الآيات الموجودة: ${Object.keys(surahNas.verse).join(', ')}`);
  
  console.log('\n📝 نص الآيات:');
  for (let i = 1; i <= 6; i++) {
    const verse = surahNas.verse[`verse_${i}`];
    if (verse) {
      console.log(`   آية ${i}: ${verse}`);
    } else {
      console.log(`   آية ${i}: ❌ مفقودة`);
    }
  }
} catch (error) {
  console.log(`❌ خطأ في قراءة سورة الناس: ${error.message}`);
}

console.log('\n📊 ملخص النتائج:');
if (allCorrect) {
  console.log('✅ جميع السور المفحوصة صحيحة!');
} else {
  console.log('❌ توجد مشاكل في بعض السور - يرجى المراجعة');
}

console.log('\n🔧 فحص ربط السور في quranData.ts:');
try {
  // قراءة ملف quranData للتحقق من الربط
  const quranDataContent = fs.readFileSync('./src/data/quranData.ts', 'utf8');
  
  if (quranDataContent.includes('parseInt(surah.index)')) {
    console.log('✅ ربط السور يستخدم parseInt للفهرس');
  } else {
    console.log('⚠️ قد تكون هناك مشكلة في ربط السور');
  }
  
  if (quranDataContent.includes('surah.titleAr')) {
    console.log('✅ ربط السور يستخدم الاسم العربي');
  } else {
    console.log('⚠️ قد تكون هناك مشكلة في ربط الأسماء العربية');
  }
  
} catch (error) {
  console.log(`❌ خطأ في قراءة quranData.ts: ${error.message}`);
}

console.log('\n🎯 التوصيات:');
console.log('1. تأكد من أن جميع ملفات السور موجودة ومرقمة بشكل صحيح');
console.log('2. تحقق من أن الفهارس في ملفات JSON تطابق أرقام الملفات');
console.log('3. تأكد من أن عدد الآيات في كل سورة صحيح');
console.log('4. اختبر التطبيق للتأكد من عرض المحتوى الصحيح');
