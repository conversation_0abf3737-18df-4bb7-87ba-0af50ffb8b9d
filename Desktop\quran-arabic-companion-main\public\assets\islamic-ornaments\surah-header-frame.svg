<svg width="400" height="200" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Main Islamic Frame -->
  <path d="M50 50 
           Q50 30 70 30 
           L130 30 
           Q150 10 200 10 
           Q250 10 270 30 
           L330 30 
           Q350 30 350 50 
           L350 80 
           Q370 100 370 120 
           Q370 140 350 160 
           L350 170 
           Q350 190 330 190 
           L270 190 
           Q250 210 200 210 
           Q150 210 130 190 
           L70 190 
           Q50 190 50 170 
           L50 160 
           Q30 140 30 120 
           Q30 100 50 80 
           Z" 
        fill="none" 
        stroke="url(#goldGradient)" 
        stroke-width="3" 
        filter="url(#glow)"/>
  
  <!-- Inner decorative frame -->
  <path d="M70 60 
           Q70 45 85 45 
           L140 45 
           Q160 30 200 30 
           Q240 30 260 45 
           L315 45 
           Q330 45 330 60 
           L330 85 
           Q345 100 345 120 
           Q345 140 330 155 
           L330 160 
           Q330 175 315 175 
           L260 175 
           Q240 190 200 190 
           Q160 190 140 175 
           L85 175 
           Q70 175 70 160 
           L70 155 
           Q55 140 55 120 
           Q55 100 70 85 
           Z" 
        fill="rgba(255, 215, 0, 0.1)" 
        stroke="url(#goldGradient)" 
        stroke-width="1.5"/>
  
  <!-- Decorative corner elements -->
  <circle cx="80" cy="70" r="3" fill="url(#goldGradient)"/>
  <circle cx="320" cy="70" r="3" fill="url(#goldGradient)"/>
  <circle cx="80" cy="150" r="3" fill="url(#goldGradient)"/>
  <circle cx="320" cy="150" r="3" fill="url(#goldGradient)"/>
  
  <!-- Center ornament -->
  <path d="M190 110 Q200 100 210 110 Q200 120 190 110 Z" fill="url(#goldGradient)" opacity="0.7"/>
</svg>
