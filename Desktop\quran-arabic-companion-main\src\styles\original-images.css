/* استخدام الصور الأصلية كما هي */

/* الإطار الذهبي المقوس - للسور */
.surah-header-original {
  position: relative;
  display: inline-block;
  background-image: url('/assets/images/islamic-frame-1.jpg');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  width: 400px;
  height: 200px;
  margin: 20px auto;
}

.surah-header-original .content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  width: 80%;
}

.surah-header-original .surah-name-arabic {
  font-family: '<PERSON><PERSON>', 'Scheherazade New', serif;
  font-size: 2.5rem;
  font-weight: bold;
  color: #2c3e50;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 8px;
}

.surah-header-original .surah-name-english {
  font-family: 'Inter', sans-serif;
  font-size: 1.1rem;
  color: #7f8c8d;
  font-weight: 500;
  letter-spacing: 1px;
}

/* إطار الصفحة التقليدي */
.quran-page-original {
  position: relative;
  background-image: url('/assets/images/islamic-page-border.jpg');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  min-height: 600px;
  max-width: 800px;
  margin: 20px auto;
  padding: 80px 100px;
}

.quran-page-original .page-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* البسملة داخل الإطار */
.bismillah-in-frame {
  text-align: center;
  margin: 30px 0;
  font-family: 'Amiri', 'Scheherazade New', serif;
  font-size: 2rem;
  color: #2c3e50;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* الآيات داخل الإطار */
.verses-in-frame {
  text-align: right;
  direction: rtl;
  line-height: 2.5;
  font-family: 'Scheherazade New', 'Amiri', serif;
  font-size: 1.5rem;
  color: #2c3e50;
  margin: 20px 0;
}

.verse-in-frame {
  margin-bottom: 15px;
  padding: 5px 0;
  transition: all 0.3s ease;
}

.verse-in-frame:hover {
  background-color: rgba(255, 215, 0, 0.1);
  border-radius: 8px;
  padding: 8px;
}

/* أرقام الآيات داخل الإطار */
.verse-number-in-frame {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #FFD700 0%, #DAA520 100%);
  border: 2px solid #B8860B;
  border-radius: 50%;
  color: #2c3e50;
  font-family: 'Inter', sans-serif;
  font-size: 0.8rem;
  font-weight: bold;
  margin: 0 8px;
  box-shadow: 0 2px 6px rgba(218, 165, 32, 0.3);
}

/* تخطيط مدمج للصورتين */
.combined-layout {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

/* تأثيرات تفاعلية */
.surah-header-original:hover {
  transform: scale(1.02);
  transition: transform 0.3s ease;
}

.quran-page-original:hover {
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  transition: box-shadow 0.3s ease;
}

/* تصميم متجاوب للصور الأصلية */
@media (max-width: 768px) {
  .surah-header-original {
    width: 300px;
    height: 150px;
  }
  
  .surah-header-original .surah-name-arabic {
    font-size: 2rem;
  }
  
  .surah-header-original .surah-name-english {
    font-size: 1rem;
  }
  
  .quran-page-original {
    padding: 60px 40px;
    min-height: 500px;
  }
  
  .verses-in-frame {
    font-size: 1.3rem;
  }
  
  .bismillah-in-frame {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .surah-header-original {
    width: 250px;
    height: 125px;
  }
  
  .surah-header-original .surah-name-arabic {
    font-size: 1.5rem;
  }
  
  .quran-page-original {
    padding: 40px 20px;
    min-height: 400px;
  }
  
  .verses-in-frame {
    font-size: 1.2rem;
  }
  
  .bismillah-in-frame {
    font-size: 1.3rem;
  }
  
  .verse-number-in-frame {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
  }
}

/* فئات مساعدة للتحكم في الصور */
.image-fit-contain {
  object-fit: contain;
}

.image-fit-cover {
  object-fit: cover;
}

.image-center {
  object-position: center;
}

/* تأثيرات خاصة للصور */
.image-glow {
  filter: drop-shadow(0 4px 8px rgba(255, 215, 0, 0.3));
}

.image-hover-glow:hover {
  filter: drop-shadow(0 6px 12px rgba(255, 215, 0, 0.5));
  transition: filter 0.3s ease;
}

/* خلفيات متدرجة لتحسين قراءة النص فوق الصور */
.text-overlay-gradient {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 50%,
    rgba(255, 255, 255, 0.9) 100%
  );
  border-radius: 8px;
  padding: 10px 20px;
  backdrop-filter: blur(2px);
}

/* تحسينات للنص فوق الصور */
.text-on-image {
  text-shadow: 
    0 1px 2px rgba(0,0,0,0.5),
    0 2px 4px rgba(0,0,0,0.3);
}

.text-on-image-light {
  text-shadow: 
    0 1px 2px rgba(255,255,255,0.8),
    0 2px 4px rgba(255,255,255,0.6);
}
