import fs from 'fs';

console.log('🧪 اختبار API الرسمي للقرآن الكريم...\n');

// استخدام fetch بدلاً من http
async function fetchPageData(pageNumber) {
  try {
    const url = `http://api.alquran.cloud/v1/page/${pageNumber}/quran-uthmani`;
    console.log(`جاري جلب الصفحة ${pageNumber}...`);
    
    // استخدام dynamic import للحصول على fetch
    const { default: fetch } = await import('node-fetch');
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.code === 200) {
      return data.data;
    } else {
      throw new Error(`API Error: ${data.status}`);
    }
  } catch (error) {
    console.error(`خطأ في جلب الصفحة ${pageNumber}:`, error.message);
    return null;
  }
}

// دالة لتحويل النص العربي من Unicode
function decodeArabicText(text) {
  return text.replace(/\\u[\dA-F]{4}/gi, (match) => {
    return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
  });
}

// دالة لمعالجة صفحة واحدة
function processPageData(pageData, pageNumber) {
  // تجميع السور في هذه الصفحة
  const surahsMap = {};
  
  pageData.ayahs.forEach(ayah => {
    const surahNumber = ayah.surah.number;
    const surahName = decodeArabicText(ayah.surah.name).replace('سُورَةُ ', '');
    const verseNumber = ayah.numberInSurah;
    
    if (!surahsMap[surahNumber]) {
      surahsMap[surahNumber] = {
        surah: surahNumber,
        name: surahName,
        verses: []
      };
    }
    
    surahsMap[surahNumber].verses.push(verseNumber);
  });
  
  // تحويل إلى مصفوفة
  const surahs = Object.values(surahsMap);
  
  return {
    page: pageNumber,
    juz: pageData.ayahs[0]?.juz || 1,
    surahs: surahs
  };
}

// اختبار بعض الصفحات المهمة
async function testImportantPages() {
  const testPages = [1, 582, 583, 604];
  const results = [];
  
  for (const pageNum of testPages) {
    const pageData = await fetchPageData(pageNum);
    
    if (pageData) {
      const processedPage = processPageData(pageData, pageNum);
      results.push(processedPage);
      
      console.log(`✅ الصفحة ${pageNum}:`);
      console.log(`   الجزء: ${processedPage.juz}`);
      console.log(`   السور: ${processedPage.surahs.map(s => `${s.name} (${s.verses.length} آية)`).join(', ')}`);
      console.log('');
    }
    
    // توقف قصير بين الطلبات
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // حفظ النتائج للاختبار
  fs.writeFileSync('./test_pages.json', JSON.stringify(results, null, 2), 'utf8');
  
  console.log('✅ تم حفظ نتائج الاختبار في test_pages.json');
  
  return results;
}

// تشغيل الاختبار
testImportantPages().catch(error => {
  console.error('خطأ في الاختبار:', error);
});
