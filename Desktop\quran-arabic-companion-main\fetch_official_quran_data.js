import fs from 'fs';
import http from 'http';

console.log('📖 جلب البيانات الرسمية من API القرآن الكريم...\n');

// دالة لجلب البيانات من API
function fetchPageData(pageNumber) {
  return new Promise((resolve, reject) => {
    const url = `http://api.alquran.cloud/v1/page/${pageNumber}/quran-uthmani`;
    
    http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          if (jsonData.code === 200) {
            resolve(jsonData.data);
          } else {
            reject(new Error(`API Error: ${jsonData.status}`));
          }
        } catch (error) {
          reject(error);
        }
      });
    }).on('error', (error) => {
      reject(error);
    });
  });
}

// دالة لتحويل النص العربي من Unicode
function decodeArabicText(text) {
  return text.replace(/\\u[\dA-F]{4}/gi, (match) => {
    return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
  });
}

// دالة لمعالجة صفحة واحدة
async function processPage(pageNumber) {
  try {
    console.log(`جاري معالجة الصفحة ${pageNumber}...`);
    
    const pageData = await fetchPageData(pageNumber);
    
    // تجميع السور في هذه الصفحة
    const surahsMap = {};
    
    pageData.ayahs.forEach(ayah => {
      const surahNumber = ayah.surah.number;
      const surahName = decodeArabicText(ayah.surah.name).replace('سُورَةُ ', '');
      const verseNumber = ayah.numberInSurah;
      
      if (!surahsMap[surahNumber]) {
        surahsMap[surahNumber] = {
          surah: surahNumber,
          name: surahName,
          verses: []
        };
      }
      
      surahsMap[surahNumber].verses.push(verseNumber);
    });
    
    // تحويل إلى مصفوفة
    const surahs = Object.values(surahsMap);
    
    return {
      page: pageNumber,
      juz: pageData.ayahs[0]?.juz || 1,
      surahs: surahs
    };
    
  } catch (error) {
    console.error(`خطأ في معالجة الصفحة ${pageNumber}:`, error.message);
    return null;
  }
}

// دالة رئيسية لمعالجة جميع الصفحات
async function createOfficialPagesFile() {
  const pages = [];
  
  // معالجة الصفحات بشكل تدريجي لتجنب إرهاق الخادم
  for (let pageNum = 1; pageNum <= 604; pageNum++) {
    const pageData = await processPage(pageNum);
    
    if (pageData) {
      pages.push(pageData);
    }
    
    // توقف قصير بين الطلبات
    if (pageNum % 10 === 0) {
      console.log(`تم معالجة ${pageNum} صفحة...`);
      await new Promise(resolve => setTimeout(resolve, 1000)); // توقف ثانية واحدة
    }
  }
  
  // حفظ الملف
  fs.writeFileSync('./src/data/pages.json', JSON.stringify(pages, null, 2), 'utf8');
  
  console.log(`\n✅ تم إنشاء ملف pages.json رسمي مع ${pages.length} صفحة`);
  
  // التحقق من بعض الصفحات المهمة
  console.log('\n🔍 التحقق من الصفحات المهمة:');
  
  const page1 = pages.find(p => p.page === 1);
  if (page1) {
    console.log(`الصفحة 1: ${page1.surahs.map(s => s.name).join(', ')}`);
  }
  
  const page582 = pages.find(p => p.page === 582);
  if (page582) {
    console.log(`الصفحة 582: ${page582.surahs.map(s => s.name).join(', ')}`);
  }
  
  const page604 = pages.find(p => p.page === 604);
  if (page604) {
    console.log(`الصفحة 604: ${page604.surahs.map(s => s.name).join(', ')}`);
  }
}

// تشغيل السكريبت
createOfficialPagesFile().catch(error => {
  console.error('خطأ في تشغيل السكريبت:', error);
});

console.log('🚀 بدء جلب البيانات الرسمية...');
