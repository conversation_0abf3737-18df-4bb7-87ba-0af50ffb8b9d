const fs = require('fs');

// اقرأ الملف النصي
const lines = fs.readFileSync('quran.txt', 'utf8').split('\n');

// حول كل سطر إلى كائن JSON
const result = lines.filter(Boolean).map(line => {
  const [surah, ayah, text, page] = line.split('|');
  return { surah: +surah, ayah: +ayah, text, page: +page };
});

// احفظ النتيجة في ملف quran.json
fs.writeFileSync('quran.json', JSON.stringify(result, null, 2), 'utf8');
console.log('تم التحويل! ملف quran.json جاهز.'); 