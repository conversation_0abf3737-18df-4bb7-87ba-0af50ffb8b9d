import { useState, useEffect } from "react";
import { ArrowRight } from "lucide-react";

interface QuranSettingsProps {
  onBack: () => void;
  onFontSizeChange: (size: number) => void;
  onSettingsChange?: (settings: {
    selectedMushaf: 'connected' | 'separate',
    selectedColor: string,
    selectedMode: 'dark' | 'light' | 'auto',
    fontSize: number
  }) => void;
}

const QuranSettings = ({ onBack, onFontSizeChange, onSettingsChange }: QuranSettingsProps) => {
  const [selectedMushaf, setSelectedMushaf] = useState<'connected' | 'separate'>(
    () => localStorage.getItem('selectedMushaf') as 'connected' | 'separate' || 'connected'
  );
  const [selectedColor, setSelectedColor] = useState(
    () => localStorage.getItem('selectedColor') || 'gray'
  );
  const [selectedMode, setSelectedMode] = useState<'dark' | 'light' | 'auto'>(
    () => localStorage.getItem('selectedMode') as 'dark' | 'light' | 'auto' || 'dark'
  );
  const [fontSize, setFontSize] = useState(() => {
    const savedFontSize = localStorage.getItem('fontSize');
    return savedFontSize ? parseInt(savedFontSize) : 20;
  });
  const [pageNumberInput, setPageNumberInput] = useState('1234');

  useEffect(() => {
    localStorage.setItem('selectedMushaf', selectedMushaf);
    onSettingsChange && onSettingsChange({
      selectedMushaf,
      selectedColor,
      selectedMode,
      fontSize
    });
  }, [selectedMushaf]);

  useEffect(() => {
    localStorage.setItem('selectedColor', selectedColor);
    onSettingsChange && onSettingsChange({
      selectedMushaf,
      selectedColor,
      selectedMode,
      fontSize
    });
  }, [selectedColor]);

  useEffect(() => {
    localStorage.setItem('selectedMode', selectedMode);
    document.documentElement.classList.remove('dark', 'light');
    if (selectedMode === 'dark') {
      document.documentElement.classList.add('dark');
    } else if (selectedMode === 'light') {
      document.documentElement.classList.add('light');
    } else {
      if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.add('light');
      }
    }
    onSettingsChange && onSettingsChange({
      selectedMushaf,
      selectedColor,
      selectedMode,
      fontSize
    });
  }, [selectedMode]);

  useEffect(() => {
    localStorage.setItem('fontSize', fontSize.toString());
    onSettingsChange && onSettingsChange({
      selectedMushaf,
      selectedColor,
      selectedMode,
      fontSize
    });
  }, [fontSize]);

  const handleFontSizeChange = (increment: number) => {
    setFontSize((prevSize) => Math.max(16, prevSize + increment)); // Min font size of 16
  };

  const handleFontSizeReset = () => {
    setFontSize(20);
  };

  const colors = [
    { name: 'red', color: 'bg-red-500' },
    { name: 'gray', color: 'bg-gray-300' },
    { name: 'blue', color: 'bg-blue-500' },
    { name: 'green', color: 'bg-green-500' },
    { name: 'dark', color: 'bg-gray-600' }
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gray-900 p-4 flex items-center justify-between">
        <div></div>
        <h1 className="text-lg font-bold">إعدادات المصحف</h1>
        <button onClick={onBack} className="text-green-400">
          <ArrowRight size={20} />
        </button>
      </div>

      <div className="p-4 space-y-6">
        {/* Mushaf selection */}
        <div>
          <h3 className="text-white font-bold mb-4 text-right">المصحف 1</h3>
          <div 
            onClick={() => setSelectedMushaf('connected')}
            className={`rounded-lg p-3 text-right cursor-pointer ${
              selectedMushaf === 'connected' ? 'bg-gray-700' : 'bg-gray-800'
            }`}
          >
            <span className={selectedMushaf === 'connected' ? 'text-green-400' : 'text-gray-400'}>
              {selectedMushaf === 'connected' ? '✓' : ''}
            </span>
            <span className="mr-3 text-white">آيات متصلة</span>
          </div>
          <div 
            onClick={() => setSelectedMushaf('separate')}
            className={`rounded-lg p-3 mt-2 text-right cursor-pointer ${
              selectedMushaf === 'separate' ? 'bg-gray-700' : 'bg-gray-800'
            }`}
          >
            <span className={selectedMushaf === 'separate' ? 'text-green-400' : 'text-gray-400'}>
              {selectedMushaf === 'separate' ? '✓' : ''}
            </span>
            <span className="mr-3 text-white">آيات غير متصلة</span>
          </div>
        </div>

        {/* Surah name (display only for now) */}
        <div>
          <h3 className="text-white font-bold mb-4 text-right">اسم السورة</h3>
          <div className="bg-gray-800 rounded-lg p-4 text-center">
            <div className="border-2 border-green-500 rounded-lg p-3 inline-block">
              <span className="text-white text-lg">سُورَةُ ٱلْبَقَرَة</span>
            </div>
          </div>
        </div>

        {/* Font Size Setting */}
        <div>
          <h3 className="text-white font-bold mb-4 text-right">حجم النص</h3>
          <div className="flex justify-center items-center gap-4">
            <button 
              onClick={() => handleFontSizeChange(-2)}
              className="bg-gray-800 p-2 rounded-full disabled:opacity-50"
            >
              -
            </button>
            <button 
              onClick={handleFontSizeReset}
              className="bg-gray-700 text-white px-4 py-2 rounded-lg"
            >
              الأفتراضي
            </button>
            <button 
              onClick={() => handleFontSizeChange(2)}
              className="bg-gray-800 p-2 rounded-full disabled:opacity-50"
            >
              +
            </button>
          </div>
        </div>

        {/* Page numbers color */}
        <div>
          <h3 className="text-white font-bold mb-4 text-right">لون الأرقام</h3>
          <div className="flex justify-center gap-4">
            {colors.map((color) => (
              <div
                key={color.name}
                onClick={() => setSelectedColor(color.name)}
                className={`w-10 h-10 ${color.color} rounded-full cursor-pointer ${
                  selectedColor === color.name ? 'border-2 border-green-500' : ''
                }`}
              ></div>
            ))}
          </div>
        </div>

        {/* Dark mode */}
        <div>
          <h3 className="text-white font-bold mb-4 text-right">الوضع الداكن</h3>
          <div className="flex justify-center gap-4">
            <button 
              onClick={() => setSelectedMode('dark')}
              className={`px-6 py-2 rounded-lg ${
                selectedMode === 'dark' 
                ? 'bg-gray-600 text-white border-2 border-green-500' 
                : 'bg-gray-700 text-gray-400'
              }`}
            >
              داكن
            </button>
            <button 
              onClick={() => setSelectedMode('light')}
              className={`px-6 py-2 rounded-lg ${
                selectedMode === 'light' 
                ? 'bg-gray-600 text-white border-2 border-green-500' 
                : 'bg-gray-700 text-gray-400'
              }`}
            >
              فاتح
            </button>
            <button 
              onClick={() => setSelectedMode('auto')}
              className={`px-6 py-2 rounded-lg ${
                selectedMode === 'auto' 
                ? 'bg-gray-600 text-white border-2 border-green-500' 
                : 'bg-gray-700 text-gray-400'
              }`}
            >
              تلقائي
            </button>
          </div>
        </div>

        {/* Page numbers in main interface */}
        <div>
          <h3 className="text-white font-bold mb-4 text-right">الأرقام في الواجهة الرئيسية</h3>
          <div className="bg-gray-800 rounded-lg p-4">
            <div className="flex justify-between items-center mb-4">
              <span className="text-gray-400">١٢٣٤</span>
              <input
                type="text"
                value={pageNumberInput}
                onChange={(e) => setPageNumberInput(e.target.value)}
                className="bg-gray-700 text-white text-center px-4 py-2 rounded-lg border-2 border-green-500 w-20"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuranSettings;
