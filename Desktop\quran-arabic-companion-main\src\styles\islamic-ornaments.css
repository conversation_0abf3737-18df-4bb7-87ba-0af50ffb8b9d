/* Islamic Ornaments Styles */

/* <PERSON><PERSON>er with Islamic Frame */
.surah-header-ornament {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  min-height: 120px;
}

.surah-header-frame {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 400px;
  height: auto;
  z-index: 1;
}

.surah-header-content {
  position: relative;
  z-index: 2;
  text-align: center;
  padding: 20px;
}

.surah-name-arabic {
  font-family: 'Amiri', 'Scheherazade New', serif;
  font-size: 2.5rem;
  font-weight: bold;
  color: #2c3e50;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 8px;
}

.surah-name-english {
  font-family: 'Inter', sans-serif;
  font-size: 1.1rem;
  color: #7f8c8d;
  font-weight: 500;
  letter-spacing: 1px;
}

/* Page Layout with Islamic Border */
.quran-page-ornament {
  position: relative;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.page-border-frame {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0.8;
}

.quran-page-content {
  position: relative;
  z-index: 2;
  padding: 60px 80px;
  min-height: 600px;
}

/* Bismillah Ornament */
.bismillah-ornament {
  text-align: center;
  margin: 30px 0;
  position: relative;
}

.bismillah-text {
  font-family: 'Amiri', 'Scheherazade New', serif;
  font-size: 2rem;
  color: #2c3e50;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: relative;
  display: inline-block;
}

.bismillah-text::before,
.bismillah-text::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #DAA520, transparent);
}

.bismillah-text::before {
  right: 100%;
  margin-right: 20px;
}

.bismillah-text::after {
  left: 100%;
  margin-left: 20px;
}

/* Verse Number Ornament */
.verse-number-ornament {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: radial-gradient(circle, #FFD700 0%, #DAA520 100%);
  border: 2px solid #B8860B;
  border-radius: 50%;
  color: #2c3e50;
  font-family: 'Inter', sans-serif;
  font-size: 0.9rem;
  font-weight: bold;
  margin: 0 8px;
  box-shadow: 0 2px 8px rgba(218, 165, 32, 0.3);
  position: relative;
}

.verse-number-ornament::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 1px solid rgba(218, 165, 32, 0.3);
  border-radius: 50%;
}

/* Islamic Decorative Elements */
.islamic-star {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23DAA520' d='M12 2l2.4 7.2h7.6l-6 4.8 2.4 7.2-6-4.8-6 4.8 2.4-7.2-6-4.8h7.6z'/%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
  margin: 0 4px;
}

.section-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 40px 0;
}

.section-divider::before,
.section-divider::after {
  content: '';
  flex: 1;
  height: 2px;
  background: linear-gradient(90deg, transparent, #DAA520, transparent);
}

.section-divider .islamic-star {
  margin: 0 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .surah-header-frame {
    max-width: 300px;
  }
  
  .surah-name-arabic {
    font-size: 2rem;
  }
  
  .quran-page-content {
    padding: 40px 30px;
  }
  
  .bismillah-text {
    font-size: 1.5rem;
  }
  
  .bismillah-text::before,
  .bismillah-text::after {
    width: 40px;
  }
}

@media (max-width: 480px) {
  .surah-header-frame {
    max-width: 250px;
  }
  
  .surah-name-arabic {
    font-size: 1.8rem;
  }
  
  .quran-page-content {
    padding: 30px 20px;
  }
  
  .verse-number-ornament {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }
}

/* Animation Effects */
.surah-header-ornament {
  animation: fadeInUp 0.8s ease-out;
}

.bismillah-ornament {
  animation: fadeIn 1s ease-out 0.3s both;
}

.verse-number-ornament {
  transition: all 0.3s ease;
}

.verse-number-ornament:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(218, 165, 32, 0.5);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
