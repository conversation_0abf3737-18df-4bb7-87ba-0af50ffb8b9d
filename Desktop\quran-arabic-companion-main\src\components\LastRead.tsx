import { useState, useEffect } from "react";

interface LastReadProps {
  onContinue: (page: number, aya: number) => void;
  onLastPage: (page: number) => void;
}

const LastRead = ({ onContinue, onLastPage }: LastReadProps) => {
  const [lastReadAya, setLastReadAya] = useState<{ surahName: string; ayaNumber: number; pageNumber: number; timestamp: string } | null>(null);
  const [lastReadPage, setLastReadPage] = useState<{ surahName: string; pageNumber: number; timestamp: string } | null>(null);
  const [savedAyaBookmarks, setSavedAyaBookmarks] = useState<any[]>([]);
  const [savedPageBookmarks, setSavedPageBookmarks] = useState<any[]>([]);

  useEffect(() => {
    const savedLastReadAya = localStorage.getItem('lastReadAya');
    if (savedLastReadAya) {
      setLastReadAya(JSON.parse(savedLastReadAya));
    }
    const savedLastReadPage = localStorage.getItem('lastReadPage');
    if (savedLastReadPage) {
      setLastReadPage(JSON.parse(savedLastReadPage));
    }

    // Load colored bookmarks - unlimited
    const savedAyas = JSON.parse(localStorage.getItem('lastReadAyas') || '[]');
    setSavedAyaBookmarks(savedAyas);

    const savedPages = JSON.parse(localStorage.getItem('lastReadPages') || '[]');
    setSavedPageBookmarks(savedPages);
  }, []);

  // Listen for storage changes to update bookmarks in real-time
  useEffect(() => {
    const handleStorageChange = () => {
      const savedAyas = JSON.parse(localStorage.getItem('lastReadAyas') || '[]');
      setSavedAyaBookmarks(savedAyas);

      const savedPages = JSON.parse(localStorage.getItem('lastReadPages') || '[]');
      setSavedPageBookmarks(savedPages);
    };

    window.addEventListener('storage', handleStorageChange);

    // Also check periodically for updates from same tab
    const interval = setInterval(handleStorageChange, 1000);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, []);

  // Remove verse bookmark
  const removeVerseBookmark = (bookmarkId: string) => {
    const updatedBookmarks = savedAyaBookmarks.filter(bookmark => bookmark.id !== bookmarkId);
    setSavedAyaBookmarks(updatedBookmarks);
    localStorage.setItem('lastReadAyas', JSON.stringify(updatedBookmarks));
  };

  // Remove page bookmark
  const removePageBookmark = (bookmarkId: string) => {
    const updatedBookmarks = savedPageBookmarks.filter(bookmark => bookmark.id !== bookmarkId);
    setSavedPageBookmarks(updatedBookmarks);
    localStorage.setItem('lastReadPages', JSON.stringify(updatedBookmarks));
  };

  const getDisplayDate = (timestamp: string) => {
    const date = new Date(timestamp);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const dateMidnight = new Date(date);
    dateMidnight.setHours(0, 0, 0, 0);

    const diffTime = Math.abs(dateMidnight.getTime() - today.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    const timeOptions: Intl.DateTimeFormatOptions = { hour: '2-digit', minute: '2-digit', hour12: false };
    const formattedTime = date.toLocaleTimeString('ar-EG', timeOptions);

    if (diffDays === 0) return `اليوم • ${formattedTime}`;
    if (diffDays === 1) return `أمس • ${formattedTime}`;
    
    const dateOptions: Intl.DateTimeFormatOptions = { weekday: 'long', month: 'numeric', day: 'numeric' };
    const formattedDate = date.toLocaleDateString('ar-EG', dateOptions);
    return `${formattedDate} • ${formattedTime}`;
  };

  return (
    <div className="bg-gray-900 p-4 space-y-4">
      {/* آخر صفحة */}
      <div>
        <h3 className="text-white font-bold mb-3 text-right">آخر صفحة</h3>
        <div className="flex gap-3 overflow-x-auto pb-2">
          {savedPageBookmarks.length > 0 ? (
            savedPageBookmarks.map((bookmark, index) => (
              <div
                key={bookmark.id}
                className="min-w-[200px] bg-gray-800 rounded-lg p-3 hover:bg-gray-700 transition-colors border-r-4 relative group"
                style={{ borderRightColor: bookmark.color }}
              >
                {/* زر الحذف */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    removePageBookmark(bookmark.id);
                  }}
                  className="absolute -top-2 -left-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity z-10"
                  title="حذف الفاصلة"
                >
                  ×
                </button>

                <div
                  onClick={() => onLastPage(bookmark.pageNumber)}
                  className="text-right cursor-pointer"
                >
                  <div className="flex items-center justify-between mb-1">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: bookmark.color }}
                    ></div>
                    <h4 className="text-white font-medium text-sm">صفحة {bookmark.pageNumber}</h4>
                  </div>
                  <p className="text-gray-400 text-xs">{bookmark.surahName} • جزء {bookmark.juzNumber}</p>
                  <p className="text-gray-500 text-xs">
                    {getDisplayDate(bookmark.timestamp)}
                  </p>
                </div>
              </div>
            ))
          ) : (
            <div
              onClick={() => lastReadPage && onLastPage(lastReadPage.pageNumber)}
              className="flex-1 bg-gray-800 rounded-lg p-4 cursor-pointer hover:bg-gray-700 transition-colors"
            >
              <div className="text-right">
                <h4 className="text-white font-bold mb-1">آخر صفحة</h4>
                <p className="text-gray-400 text-sm">{lastReadPage?.surahName || 'البقرة'} {lastReadPage?.pageNumber || 3}</p>
                <p className="text-gray-500 text-xs">
                  {lastReadPage ? getDisplayDate(lastReadPage.timestamp) : 'أمس • السبت 1:59 ص'}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* آخر آية */}
      <div>
        <h3 className="text-white font-bold mb-3 text-right">آخر آية</h3>
        <div className="flex gap-3 overflow-x-auto pb-2">
          {savedAyaBookmarks.length > 0 ? (
            savedAyaBookmarks.map((bookmark, index) => (
              <div
                key={bookmark.id}
                className="min-w-[200px] bg-gray-800 rounded-lg p-3 hover:bg-gray-700 transition-colors border-r-4 relative group"
                style={{ borderRightColor: bookmark.color }}
              >
                {/* زر الحذف */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    removeVerseBookmark(bookmark.id);
                  }}
                  className="absolute -top-2 -left-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity z-10"
                  title="حذف الفاصلة"
                >
                  ×
                </button>

                <div
                  onClick={() => onContinue(bookmark.pageNumber, bookmark.ayaNumber)}
                  className="text-right cursor-pointer"
                >
                  <div className="flex items-center justify-between mb-1">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: bookmark.color }}
                    ></div>
                    <h4 className="text-white font-medium text-sm">{bookmark.surahName}</h4>
                  </div>
                  <p className="text-gray-400 text-xs">الآية {bookmark.ayaNumber} • ص {bookmark.pageNumber}</p>
                  <p className="text-gray-500 text-xs truncate">{bookmark.verseText}</p>
                  <p className="text-gray-500 text-xs mt-1">
                    {getDisplayDate(bookmark.timestamp)}
                  </p>
                </div>
              </div>
            ))
          ) : (
            <div
              onClick={() => lastReadAya && onContinue(lastReadAya.pageNumber, lastReadAya.ayaNumber)}
              className="flex-1 bg-gray-800 rounded-lg p-4 cursor-pointer hover:bg-gray-700 transition-colors"
            >
              <div className="text-right">
                <h4 className="text-white font-bold mb-1">آخر آية • {lastReadAya?.surahName || 'البقرة'}</h4>
                <p className="text-gray-400 text-sm">الآية {lastReadAya?.ayaNumber || 62} • ص {lastReadAya?.pageNumber || 10}</p>
                <p className="text-gray-500 text-xs">
                  {lastReadAya ? getDisplayDate(lastReadAya.timestamp) : 'أمس • السبت 1:58 ص'}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LastRead;
