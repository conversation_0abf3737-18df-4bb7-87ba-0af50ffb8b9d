import React, { useState, useEffect } from 'react';
import { BookO<PERSON>, Clock, Play } from 'lucide-react';

interface LastReadingInfo {
  page: number;
  juz: number;
  surah: string;
  timestamp: string;
  progress: number;
}

interface LastReadingCardProps {
  onContinueReading: (pageNumber: number) => void;
}

const LastReadingCard: React.FC<LastReadingCardProps> = ({ onContinueReading }) => {
  const [lastReading, setLastReading] = useState<LastReadingInfo | null>(null);

  useEffect(() => {
    const savedReading = localStorage.getItem('lastReadingInfo');
    if (savedReading) {
      try {
        setLastReading(JSON.parse(savedReading));
      } catch (error) {
        console.error('خطأ في قراءة بيانات آخر قراءة:', error);
      }
    }
  }, []);

  if (!lastReading) {
    return null;
  }

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'منذ قليل';
    } else if (diffInHours < 24) {
      return `منذ ${diffInHours} ساعة`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `منذ ${diffInDays} يوم`;
    }
  };

  return (
    <div className="bg-gradient-to-r from-green-600 to-green-500 rounded-2xl p-6 mx-4 my-4 shadow-lg">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3 space-x-reverse">
          <div className="bg-white bg-opacity-20 p-2 rounded-full">
            <BookOpen className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-white font-bold text-lg">آخر قراءة</h3>
            <div className="flex items-center space-x-2 space-x-reverse text-green-100 text-sm">
              <Clock className="w-4 h-4" />
              <span>{formatDate(lastReading.timestamp)}</span>
            </div>
          </div>
        </div>
        
        <button
          onClick={() => onContinueReading(lastReading.page)}
          className="bg-white bg-opacity-20 hover:bg-opacity-30 p-3 rounded-full transition-all duration-200 btn-hover"
        >
          <Play className="w-6 h-6 text-white" />
        </button>
      </div>

      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-white">{lastReading.page}</div>
          <div className="text-green-100 text-sm">الصفحة</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-white">{lastReading.juz}</div>
          <div className="text-green-100 text-sm">الجزء</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-white truncate">{lastReading.surah}</div>
          <div className="text-green-100 text-sm">السورة</div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="flex justify-between text-green-100 text-sm mb-2">
          <span>التقدم في القرآن</span>
          <span>{lastReading.progress}%</span>
        </div>
        <div className="w-full bg-white bg-opacity-20 rounded-full h-3">
          <div 
            className="bg-white h-3 rounded-full transition-all duration-300"
            style={{ width: `${lastReading.progress}%` }}
          ></div>
        </div>
      </div>

      <button
        onClick={() => onContinueReading(lastReading.page)}
        className="w-full bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-medium py-3 rounded-lg transition-all duration-200 btn-hover flex items-center justify-center space-x-2 space-x-reverse"
      >
        <Play className="w-5 h-5" />
        <span>متابعة القراءة</span>
      </button>
    </div>
  );
};

export default LastReadingCard;
