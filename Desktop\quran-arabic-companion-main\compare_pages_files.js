import fs from 'fs';

console.log('🔍 مقارنة دقيقة بين ملفي صفحات القرآن');

const pagesOriginal = JSON.parse(fs.readFileSync('./src/data/pages.json', 'utf8'));
const pagesCorrected = JSON.parse(fs.readFileSync('./src/data/pages_corrected.json', 'utf8'));

let totalPagesMatch = pagesOriginal.length === 604 && pagesCorrected.length === 604;
let lastPageCorrect = false;
let detailedDifferences = [];

for (let i = 0; i < 604; i++) {
  const orig = pagesOriginal[i];
  const corr = pagesCorrected[i];
  if (!orig || !corr) continue;
  // Check page number
  if (orig.page !== corr.page) {
    detailedDifferences.push({
      page: i + 1,
      type: 'رقم الصفحة غير متطابق',
      original: orig.page,
      corrected: corr.page
    });
    continue;
  }
  // Check surahs
  const origSurahs = orig.surahs.map(s => s.surah + ':' + s.verses.join(',')).join('|');
  const corrSurahs = corr.surahs.map(s => s.surah + ':' + s.verses.join(',')).join('|');
  if (origSurahs !== corrSurahs) {
    detailedDifferences.push({
      page: orig.page,
      type: 'محتوى الصفحة مختلف',
      original: orig.surahs.map(s => `${s.name} [${s.verses.join(',')}]`).join(' | '),
      corrected: corr.surahs.map(s => `${s.name} [${s.verses.join(',')}]`).join(' | ')
    });
  }
}

// Check last page
const lastOrig = pagesOriginal[603];
const lastCorr = pagesCorrected[603];
if (lastCorr && lastCorr.surahs.length === 3 &&
    lastCorr.surahs[0].name === 'الإخلاص' &&
    lastCorr.surahs[1].name === 'الفلق' &&
    lastCorr.surahs[2].name === 'الناس') {
  lastPageCorrect = true;
}

console.log('------------------------------');
console.log('📋 تقرير الفروق التفصيلية:');
if (detailedDifferences.length === 0) {
  console.log('✅ جميع الصفحات متطابقة في السور والآيات.');
} else {
  detailedDifferences.slice(0, 20).forEach(diff => {
    console.log(`صفحة ${diff.page}: ${diff.type}`);
    if (diff.type === 'محتوى الصفحة مختلف') {
      console.log(`   الأصلي:   ${diff.original}`);
      console.log(`   المصحح:   ${diff.corrected}`);
    } else {
      console.log(`   الأصلي:   ${diff.original}`);
      console.log(`   المصحح:   ${diff.corrected}`);
    }
    console.log('------------------------------');
  });
  if (detailedDifferences.length > 20) {
    console.log(`...ويوجد ${detailedDifferences.length - 20} صفحات أخرى مختلفة.`);
  }
}

console.log('\n------------------------------');
console.log('📊 ملخص التحليل:');
console.log(`- عدد الصفحات في الملف الأصلي: ${pagesOriginal.length}`);
console.log(`- عدد الصفحات في الملف المصحح: ${pagesCorrected.length}`);
console.log(`- عدد الصفحات المختلفة: ${detailedDifferences.length}`);
if (lastPageCorrect) {
  console.log('✅ الصفحة الأخيرة في الملف المصحح مطابقة للمصحف (الإخلاص، الفلق، الناس)');
} else {
  console.log('❌ الصفحة الأخيرة في الملف المصحح غير مطابقة للمصحف');
}
if (totalPagesMatch) {
  console.log('✅ كلا الملفين يحتويان على 604 صفحة');
} else {
  console.log('❌ أحد الملفين لا يحتوي على 604 صفحة');
}
if (detailedDifferences.length === 0 && lastPageCorrect && totalPagesMatch) {
  console.log('\n🎯 التوصية: كلا الملفين متطابقان ومناسبان للاستخدام.');
} else if (detailedDifferences.length < 10 && lastPageCorrect) {
  console.log('\n🎯 التوصية: الملف المصحح هو الأقرب للمصحف الحقيقي.');
} else {
  console.log('\n🎯 التوصية: راجع الفروق أعلاه، الملف المصحح غالباً هو الأدق خاصة في الصفحة الأخيرة.');
} 