import fs from 'fs';
import path from 'path';

// قراءة البيانات
const surahData = JSON.parse(fs.readFileSync('./src/data/surah.json', 'utf8'));
const juzData = JSON.parse(fs.readFileSync('./src/data/juz.json', 'utf8'));
const pagesData = JSON.parse(fs.readFileSync('./src/data/pages.json', 'utf8'));

console.log('🔍 بدء التحقق من صحة بيانات المصحف...\n');

// 1. التحقق من وجود جميع السور (114 سورة)
console.log('1️⃣ التحقق من السور:');
if (surahData.length === 114) {
  console.log('✅ عدد السور صحيح: 114 سورة');
} else {
  console.log(`❌ عدد السور خاطئ: ${surahData.length} بدلاً من 114`);
}

// التحقق من وجود ملفات السور
let missingFiles = [];
for (let i = 1; i <= 114; i++) {
  const filePath = `./src/data/surah/surah_${i}.json`;
  if (!fs.existsSync(filePath)) {
    missingFiles.push(i);
  }
}

if (missingFiles.length === 0) {
  console.log('✅ جميع ملفات السور موجودة (1-114)');
} else {
  console.log(`❌ ملفات السور المفقودة: ${missingFiles.join(', ')}`);
}

// 2. التحقق من الأجزاء (30 جزء)
console.log('\n2️⃣ التحقق من الأجزاء:');
if (juzData.length === 30) {
  console.log('✅ عدد الأجزاء صحيح: 30 جزء');
} else {
  console.log(`❌ عدد الأجزاء خاطئ: ${juzData.length} بدلاً من 30`);
}

// 3. التحقق من الصفحات (604 صفحة)
console.log('\n3️⃣ التحقق من الصفحات:');
if (pagesData.length === 604) {
  console.log('✅ عدد الصفحات صحيح: 604 صفحة');
} else {
  console.log(`❌ عدد الصفحات خاطئ: ${pagesData.length} بدلاً من 604`);
}

// 4. التحقق من تسلسل الصفحات
console.log('\n4️⃣ التحقق من تسلسل الصفحات:');
let pageSequenceCorrect = true;
for (let i = 0; i < pagesData.length; i++) {
  if (pagesData[i].page !== i + 1) {
    console.log(`❌ خطأ في تسلسل الصفحة: توقع ${i + 1} لكن وجد ${pagesData[i].page}`);
    pageSequenceCorrect = false;
    break;
  }
}
if (pageSequenceCorrect) {
  console.log('✅ تسلسل الصفحات صحيح (1-604)');
}

// 5. التحقق من أرقام الأجزاء في الصفحات
console.log('\n5️⃣ التحقق من أرقام الأجزاء في الصفحات:');
let juzNumbersCorrect = true;
for (const page of pagesData) {
  if (page.juz < 1 || page.juz > 30) {
    console.log(`❌ رقم جزء خاطئ في الصفحة ${page.page}: ${page.juz}`);
    juzNumbersCorrect = false;
  }
}
if (juzNumbersCorrect) {
  console.log('✅ أرقام الأجزاء في الصفحات صحيحة (1-30)');
}

// 6. التحقق من أرقام السور في الصفحات
console.log('\n6️⃣ التحقق من أرقام السور في الصفحات:');
let surahNumbersCorrect = true;
for (const page of pagesData) {
  for (const surah of page.surahs) {
    if (surah.surah < 1 || surah.surah > 114) {
      console.log(`❌ رقم سورة خاطئ في الصفحة ${page.page}: ${surah.surah}`);
      surahNumbersCorrect = false;
    }
  }
}
if (surahNumbersCorrect) {
  console.log('✅ أرقام السور في الصفحات صحيحة (1-114)');
}

// 7. التحقق من وجود آيات في ملفات السور
console.log('\n7️⃣ التحقق من محتوى ملفات السور:');
let surahFilesCorrect = true;
let checkedSurahs = 0;

for (let i = 1; i <= Math.min(10, 114); i++) { // فحص أول 10 سور كعينة
  try {
    const surahFile = JSON.parse(fs.readFileSync(`./src/data/surah/surah_${i}.json`, 'utf8'));
    const expectedCount = surahData[i-1].count;
    
    // عد الآيات الفعلية (باستثناء verse_0 وهي البسملة)
    const actualVerses = Object.keys(surahFile.verse).filter(key => key !== 'verse_0').length;
    
    if (actualVerses === expectedCount) {
      checkedSurahs++;
    } else {
      console.log(`❌ عدد آيات خاطئ في السورة ${i}: توقع ${expectedCount} لكن وجد ${actualVerses}`);
      surahFilesCorrect = false;
    }
  } catch (error) {
    console.log(`❌ خطأ في قراءة ملف السورة ${i}: ${error.message}`);
    surahFilesCorrect = false;
  }
}

if (surahFilesCorrect) {
  console.log(`✅ محتوى ملفات السور صحيح (تم فحص ${checkedSurahs} سور)`);
}

// 8. التحقق من أسماء السور
console.log('\n8️⃣ التحقق من أسماء السور:');
const expectedSurahs = [
  'الفاتحة', 'البقرة', 'آل عمران', 'النساء', 'المائدة', 'الأنعام', 'الأعراف', 'الأنفال', 'التوبة', 'يونس'
];

let surahNamesCorrect = true;
for (let i = 0; i < Math.min(10, expectedSurahs.length); i++) {
  if (surahData[i].titleAr !== expectedSurahs[i]) {
    console.log(`❌ اسم السورة ${i+1} خاطئ: توقع "${expectedSurahs[i]}" لكن وجد "${surahData[i].titleAr}"`);
    surahNamesCorrect = false;
  }
}

if (surahNamesCorrect) {
  console.log('✅ أسماء السور صحيحة (تم فحص أول 10 سور)');
}

console.log('\n🎉 انتهى التحقق من البيانات!');
console.log('📊 ملخص النتائج:');
console.log(`- السور: ${surahData.length}/114`);
console.log(`- الأجزاء: ${juzData.length}/30`);
console.log(`- الصفحات: ${pagesData.length}/604`);
