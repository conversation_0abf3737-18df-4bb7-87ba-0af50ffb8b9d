import cv2
import numpy as np
import os

input_folder = "public/images"
output_folder = "public/images_cropped1"

os.makedirs(output_folder, exist_ok=True)

def crop_by_pixels(img, top=50, bottom=50, left=50, right=50):
    h, w = img.shape[:2]
    y0 = top
    y1 = h - bottom
    x0 = left
    x1 = w - right
    # تأكد أن القيم لا تتجاوز أبعاد الصورة
    y0 = max(0, y0)
    y1 = min(h, y1)
    x0 = max(0, x0)
    x1 = min(w, x1)
    return img[y0:y1, x0:x1]

for filename in os.listdir(input_folder):
    if filename.lower().endswith(".jpg"):
        img_path = os.path.join(input_folder, filename)
        img = cv2.imread(img_path)
        cropped = crop_by_pixels(img, top=50, bottom=50, left=50, right=50)
        out_path = os.path.join(output_folder, filename)
        cv2.imwrite(out_path, cropped, [int(cv2.IMWRITE_JPEG_QUALITY), 100])
        print(f"Cropped: {filename}")

print("تم قص جميع الصور (70px أعلى/أسفل/يمين/يسار) وحفظها في:", output_folder)
