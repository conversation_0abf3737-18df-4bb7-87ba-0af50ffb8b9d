import { useState, useEffect } from "react";
import QuranHeader from "../components/QuranHeader";
import QuranTabs from "../components/QuranTabs";
import SuraCard from "../components/SuraCard";
import JuzCard from "../components/JuzCard";
import LastRead from "../components/LastRead";
import LastReadingCard from "../components/LastReadingCard";
import QuranReader from "../components/QuranReader";
import PageReader from "../components/PageReader";
import PagesList from "../components/PagesList";
import QuranSettings from "../components/QuranSettings";
import KhatmaTracker from "../components/KhatmaTracker";
import { suras, ajzaa, quranData } from "../data/quranData";

// Add PageCard component
const PageCard = ({ pageNumber, onClick }: { pageNumber: number; onClick: () => void }) => {
  return (
    <button
      onClick={onClick}
      className="bg-gray-800 rounded-lg p-3 text-center hover:bg-green-700 transition-colors text-white font-bold flex flex-col items-center justify-center"
    >
      <span className="text-lg">{pageNumber}</span>
      <span className="text-xs text-gray-400 mt-1">صفحة</span>
    </button>
  );
};

const Index = () => {
  const [activeTab, setActiveTab] = useState<'suras' | 'ajza' | 'pages'>('suras');
  const [currentView, setCurrentView] = useState<'home' | 'reader' | 'settings' | 'khatma' | 'pageReader' | 'pagesList'>('home');
  const [selectedSuraId, setSelectedSuraId] = useState<number | null>(null);
  const [selectedPageNumber, setSelectedPageNumber] = useState<number | null>(null);
  const [savedSuraId, setSavedSuraId] = useState(1);
  const [suraInput, setSuraInput] = useState('1');

  const [lastReadAyaState, setLastReadAyaState] = useState<{ surahName: string; ayaNumber: number; suraId: number; timestamp: string } | null>(null);

  const [quranSettings, setQuranSettings] = useState({
    selectedMushaf: 'connected',
    selectedColor: 'gray',
    selectedMode: 'dark',
    fontSize: 20
  });

  useEffect(() => {
    const savedLastReadAya = localStorage.getItem('lastReadAya');
    if (savedLastReadAya) {
      setLastReadAyaState(JSON.parse(savedLastReadAya));
    }
  }, []);

  const saveLastReadSuraAndAyaToLocalStorage = (suraId: number, ayaId: number) => {
    const sura = quranData.find((s: any) => s.id === suraId);
    const newLastReadAya = {
      surahName: sura?.name || 'غير معروف',
      ayaNumber: ayaId,
      suraId: suraId,
      timestamp: new Date().toISOString(),
    };
    localStorage.setItem('lastReadAya', JSON.stringify(newLastReadAya));
    setLastReadAyaState(newLastReadAya);
  };

  const handleSuraClick = (suraId: number) => {
    // البحث عن الصفحة الأولى للسورة
    const sura = suras.find(s => s.id === suraId);
    if (sura && sura.page) {
      setSelectedPageNumber(sura.page);
      setCurrentView('pageReader');
    }
  };

  const handleJuzClick = (juzId: number) => {
    console.log('Juz clicked:', juzId);
    const juz = ajzaa.find(j => j.id === juzId);
    if (juz && juz.page) {
      // التأكد من أن الصفحة صحيحة (بداية الجزء وليس قبلها)
      const correctPage = juz.page;
      console.log(`Opening Juz ${juzId} at page ${correctPage}`);
      setSelectedPageNumber(correctPage);
      setCurrentView('pageReader');
    }
  };

  const handleContinueFromAya = (suraId: number, ayaId: number) => {
    // البحث عن الصفحة الصحيحة للسورة
    const sura = suras.find(s => s.id === suraId);
    if (sura && sura.page) {
      setSelectedPageNumber(sura.page);
      setCurrentView('pageReader');
      saveLastReadSuraAndAyaToLocalStorage(suraId, ayaId);
    }
  };

  const handlePageClick = (pageNumber: number) => {
    setSelectedPageNumber(pageNumber);
    setCurrentView('pageReader');
  };

  const handleShowPagesList = () => {
    setCurrentView('pagesList');
  };

  if (currentView === 'reader' && selectedSuraId !== null) {
    return (
      <QuranReader
        initialSuraId={selectedSuraId}
        onBack={() => setCurrentView('home')}
        onShowSettings={() => setCurrentView('settings')}
        onAyaRead={saveLastReadSuraAndAyaToLocalStorage}
        quranSettings={quranSettings}
      />
    );
  }

  if (currentView === 'pageReader' && selectedPageNumber !== null) {
    return (
      <PageReader
        initialPage={selectedPageNumber}
        onBack={() => setCurrentView('home')}
      />
    );
  }

  if (currentView === 'pagesList') {
    return (
      <PagesList
        onPageSelect={(pageNumber) => {
          setSelectedPageNumber(pageNumber);
          setCurrentView('pageReader');
        }}
        currentPage={selectedPageNumber || 1}
        onBack={() => setCurrentView('home')}
      />
    );
  }

  if (currentView === 'settings') {
    return <QuranSettings onBack={() => setCurrentView('home')} onSettingsChange={setQuranSettings} />;
  }

  if (currentView === 'khatma') {
    return <KhatmaTracker onBack={() => setCurrentView('home')} />;
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <QuranHeader
        title="المصحف"
        onSearch={() => console.log('Search clicked')}
        onSettings={() => setCurrentView('settings')}
      />
      
      <LastRead
        onContinue={(pageNumber, ayaNumber) => {
          setSelectedPageNumber(pageNumber);
          setCurrentView('pageReader');
        }}
        onLastPage={(pageNumber) => {
          setSelectedPageNumber(pageNumber);
          setCurrentView('pageReader');
        }}
      />

      <LastReadingCard
        onContinueReading={(pageNumber) => {
          setSelectedPageNumber(pageNumber);
          setCurrentView('pageReader');
        }}
      />

      {/* Khatma Section */}
      <div className="bg-gray-900 p-4">
        <div
          onClick={() => setCurrentView('khatma')}
          className="bg-gray-800 rounded-lg p-4 cursor-pointer hover:bg-gray-700 transition-colors flex items-center justify-between"
        >
          <svg width="40" height="40" viewBox="0 0 40 40" fill="none" className="text-green-400">
            <path d="M20 5 L30 15 L30 25 L20 35 L10 25 L10 15 Z" stroke="currentColor" strokeWidth="2" fill="none"/>
            <path d="M15 17 L25 17 M15 20 L25 20 M15 23 L25 23" stroke="currentColor" strokeWidth="1"/>
          </svg>
          <div className="text-right">
            <h3 className="text-white font-bold">ختمة</h3>
          </div>
        </div>
      </div>
      
      <QuranTabs activeTab={activeTab} onTabChange={setActiveTab} />
      
      <div className="p-4 space-y-3 pb-20">
        {activeTab === 'suras' && (
          <div className="grid grid-cols-2 gap-3">
            {suras.map((sura, index) => (
              <div key={sura.id} className={`pb-3 ${index < suras.length - 1 ? 'border-b border-gray-700 mb-3' : ''}`}>
                <SuraCard
                  id={sura.id}
                  name={sura.name}
                  arabicName={sura.arabicName}
                  verses={sura.verses}
                  type={sura.type}
                  page={sura.page}
                  onClick={() => handleSuraClick(sura.id)}
                />
              </div>
            ))}
          </div>
        )}
        
        {activeTab === 'ajza' && (
          <div className="grid grid-cols-2 gap-3">
            {ajzaa.map((juz) => (
              <JuzCard
                key={juz.id}
                id={juz.id}
                name={juz.name}
                page={juz.page}
                onClick={() => handleJuzClick(juz.id)}
              />
            ))}
          </div>
        )}

        {/* تبويب الصفحات */}
        {activeTab === 'pages' && (
          <div className="space-y-4">
            <button
              onClick={handleShowPagesList}
              className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-bold transition-colors"
            >
              عرض قائمة الصفحات التفصيلية
            </button>

            <div className="grid grid-cols-4 gap-3">
              {Array.from({ length: 604 }, (_, i) => i + 1).map((pageNum) => (
                <PageCard
                  key={pageNum}
                  pageNumber={pageNum}
                  onClick={() => handlePageClick(pageNum)}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Index;
