import fs from 'fs';

console.log('🔍 مقارنة ملفات صفحات القرآن');
console.log('📊 تحليل دقة البيانات في كلا الملفين\n');

try {
  // قراءة الملفين
  const pagesOriginal = JSON.parse(fs.readFileSync('./src/data/pages.json', 'utf8'));
  const pagesCorrected = JSON.parse(fs.readFileSync('./src/data/pages_corrected.json', 'utf8'));

  console.log(`📄 عدد الصفحات في الملف الأصلي: ${pagesOriginal.length}`);
  console.log(`📄 عدد الصفحات في الملف المصحح: ${pagesCorrected.length}\n`);

  // تحليل الصفحات الأخيرة
  console.log('📖 تحليل الصفحات الأخيرة:\n');

  const lastOriginal = pagesOriginal[pagesOriginal.length - 1];
  const lastCorrected = pagesCorrected[pagesCorrected.length - 1];

  console.log(`الصفحة الأخيرة في الملف الأصلي (${lastOriginal.page}):`);
  lastOriginal.surahs.forEach(surah => {
    console.log(`   - سورة ${surah.name} (${surah.surah}): ${surah.verses.length} آية`);
  });

  console.log(`\nالصفحة الأخيرة في الملف المصحح (${lastCorrected.page}):`);
  lastCorrected.surahs.forEach(surah => {
    console.log(`   - سورة ${surah.name} (${surah.surah}): ${surah.verses.length} آية`);
  });

  // تحليل الأجزاء
  console.log('\n📚 تحليل الأجزاء:\n');

  const originalJuz = {};
  const correctedJuz = {};

  pagesOriginal.forEach(page => {
    if (!originalJuz[page.juz]) originalJuz[page.juz] = [];
    originalJuz[page.juz].push(page.page);
  });

  pagesCorrected.forEach(page => {
    if (!correctedJuz[page.juz]) correctedJuz[page.juz] = [];
    correctedJuz[page.juz].push(page.page);
  });

  console.log('عدد الصفحات في كل جزء:');
  for (let juz = 1; juz <= 30; juz++) {
    const originalCount = originalJuz[juz]?.length || 0;
    const correctedCount = correctedJuz[juz]?.length || 0;
    
    if (originalCount !== correctedCount) {
      console.log(`   الجزء ${juz}: ${originalCount} (أصلي) vs ${correctedCount} (مصحح) ❌`);
    } else {
      console.log(`   الجزء ${juz}: ${originalCount} صفحة ✅`);
    }
  }

  // التوصية
  console.log('\n🎯 التوصية:\n');

  if (pagesOriginal.length === 604 && pagesCorrected.length === 604) {
    console.log('✅ كلا الملفين يحتويان على 604 صفحة (العدد الصحيح)');
  } else {
    console.log(`❌ الملف الأصلي: ${pagesOriginal.length} صفحة`);
    console.log(`❌ الملف المصحح: ${pagesCorrected.length} صفحة`);
  }

  if (lastOriginal.surahs.length === 5 && lastCorrected.surahs.length === 3) {
    console.log('✅ الملف المصحح أكثر دقة - الصفحة الأخيرة تحتوي على 3 سور فقط');
  } else if (lastOriginal.surahs.length === 3 && lastCorrected.surahs.length === 3) {
    console.log('✅ كلا الملفين صحيحان - الصفحة الأخيرة تحتوي على 3 سور');
  } else {
    console.log(`❌ الملف الأصلي: ${lastOriginal.surahs.length} سور في الصفحة الأخيرة`);
    console.log(`❌ الملف المصحح: ${lastCorrected.surahs.length} سور في الصفحة الأخيرة`);
  }

  console.log('\n📋 ملخص سريع:');
  console.log(`   الملف الأصلي: ${pagesOriginal.length} صفحة`);
  console.log(`   الملف المصحح: ${pagesCorrected.length} صفحة`);
  console.log(`   الصفحة الأخيرة الأصلي: ${lastOriginal.surahs.length} سور`);
  console.log(`   الصفحة الأخيرة المصحح: ${lastCorrected.surahs.length} سور`);

} catch (error) {
  console.error('❌ خطأ في قراءة الملفات:', error.message);
} 