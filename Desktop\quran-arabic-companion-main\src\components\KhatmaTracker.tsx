import { useState, useEffect, useMemo } from "react";
import { ArrowRight, ChevronRight, ChevronLeft, Check } from "lucide-react";
import { suras, ajzaa } from "../data/quranData";

interface KhatmaTrackerProps {
  onBack: () => void;
}

type KhatmaView = 'initial' | 'selectKhatma' | 'dailyReading' | 'reminder' | 'khatmaDetails' | 'confirmKhatma';

interface SavedKhatma {
  id: string;
  duration: number;
  startDate: string;
  completedReadings: number[];
  schedule: string;
  name: string;
}

const KhatmaTracker = ({ onBack }: KhatmaTrackerProps) => {
  const [khatmaView, setKhatmaView] = useState<KhatmaView>('initial');
  const [selectedKhatmaDuration, setSelectedKhatmaDuration] = useState<number | null>(null);
  const [selectedSchedule, setSelectedSchedule] = useState<'اليوم' | 'غداً' | 'بعد غد'>('اليوم');
  const [currentDay, setCurrentDay] = useState(1);
  const [completedReadings, setCompletedReadings] = useState<number[]>([]);
  const [selectedDayReading, setSelectedDayReading] = useState<any | null>(null);
  const [reminderEnabled, setReminderEnabled] = useState(true);
  const [reminderTime, setReminderTime] = useState(() => {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  });
  const [savedKhatmas, setSavedKhatmas] = useState<SavedKhatma[]>([]);

  const generateDailyReadings = (duration: number) => {
    const totalPages = 604; // Total pages in Quran
    const pagesPerDay = Math.ceil(totalPages / duration);
    const readings = [];
    let currentPage = 1;

    for (let day = 1; day <= duration; day++) {
      const startPage = currentPage;
      let endPage = Math.min(totalPages, startPage + pagesPerDay - 1);

      // Adjust endPage to end at Juz boundary if close enough and not last day
      if (day < duration) {
        const potentialNextJuz = ajzaa.find(juz => parseInt(juz.start.index) > startPage + (pagesPerDay / 2)); // Look for juz in the second half of the daily reading
        if (potentialNextJuz && parseInt(potentialNextJuz.start.index) <= endPage) {
          endPage = parseInt(potentialNextJuz.start.index) - 1;
        } else {
            const nextJuz = ajzaa.find(juz => parseInt(juz.start.index) > endPage);
            if (nextJuz && (parseInt(nextJuz.start.index) - endPage) <= (pagesPerDay / 4)) { // If next juz is within quarter of a day's reading
                endPage = parseInt(nextJuz.start.index) - 1;
            }
        }
      }

      let readingTitle = "";
      const surahsInReading = suras.filter(sura => parseInt(sura.pages) >= startPage && parseInt(sura.pages) <= endPage);
      const firstSurah = surahsInReading[0];
      const lastSurah = surahsInReading[surahsInReading.length - 1];

      if (firstSurah && lastSurah) {
        if (firstSurah.index === lastSurah.index) {
          readingTitle = `سورة ${firstSurah.titleAr}`;
        } else {
          readingTitle = `من سورة ${firstSurah.titleAr} إلى سورة ${lastSurah.titleAr}`;
        }
      } else if (firstSurah) {
        readingTitle = `سورة ${firstSurah.titleAr}`;
      } else {
        readingTitle = `صفحة ${startPage} إلى ${endPage}`;
      }

      const startJuz = ajzaa.find(juz => parseInt(juz.start.index) <= startPage && (ajzaa[ajzaa.indexOf(juz) + 1] ? parseInt(ajzaa[ajzaa.indexOf(juz) + 1].start.index) > startPage : true));
      const endJuz = ajzaa.find(juz => parseInt(juz.start.index) <= endPage && (ajzaa[ajzaa.indexOf(juz) + 1] ? parseInt(ajzaa[ajzaa.indexOf(juz) + 1].start.index) > endPage : true));
      
      let juzRange = "";
      if (startJuz && endJuz) {
          if (startJuz.index === endJuz.index) {
              juzRange = `الجزء ${startJuz.index}`;
          } else {
              juzRange = `من الجزء ${startJuz.index} إلى الجزء ${endJuz.index}`;
          }
      } else if (startJuz) { // Should not happen if endJuz is not null, but for safety
          juzRange = `الجزء ${startJuz.index}`;
      }

      readings.push({
        day,
        title: readingTitle,
        fromPage: startPage,
        toPage: endPage,
        juz: juzRange,
      });

      currentPage = endPage + 1;
    }
    return readings;
  };

  const getDailyReadings = (duration: number | null) => {
    if (duration === null) return [];
    return generateDailyReadings(duration);
  };

  // Using useMemo for memoized calculation of currentDailyReadings
  const currentDailyReadings = useMemo(() => {
    return selectedKhatmaDuration ? getDailyReadings(selectedKhatmaDuration) : [];
  }, [selectedKhatmaDuration]);

  // currentDayReading can be derived from currentDailyReadings and currentDay
  const currentDayReading = useMemo(() => {
    return currentDailyReadings.find(dr => dr.day === currentDay);
  }, [currentDailyReadings, currentDay]);

  // Load saved khatmas on initial render
  useEffect(() => {
    const savedKhatmasStr = localStorage.getItem('savedKhatmas');
    if (savedKhatmasStr) {
      setSavedKhatmas(JSON.parse(savedKhatmasStr));
    }
  }, []);

  useEffect(() => {
    // Update selectedDayReading whenever currentDay or currentDailyReadings changes
    setSelectedDayReading(currentDailyReadings.find(dr => dr.day === currentDay));
  }, [currentDay, currentDailyReadings]);

  const getDisplayDateForReading = (readingDay: number) => {
    const savedKhatma = localStorage.getItem('activeKhatma');
    if (!savedKhatma) return '';

    const parsedKhatma = JSON.parse(savedKhatma);
    const startDate = new Date(parsedKhatma.startDate);
    const targetDate = new Date(startDate);
    targetDate.setDate(startDate.getDate() + (readingDay - 1));

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const targetDateMidnight = new Date(targetDate);
    targetDateMidnight.setHours(0, 0, 0, 0);

    const dayDiff = Math.floor((targetDateMidnight.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    const options: Intl.DateTimeFormatOptions = { weekday: 'long', month: 'numeric', day: 'numeric' };
    const formattedDate = targetDate.toLocaleDateString('ar-EG', options);

    if (dayDiff === 0) return `اليوم - ${targetDate.toLocaleDateString('ar-EG', { weekday: 'long' })}`;
    if (dayDiff === 1) return `غداً - ${targetDate.toLocaleDateString('ar-EG', { weekday: 'long' })}`;
    if (dayDiff === 2) return `بعد غد - ${targetDate.toLocaleDateString('ar-EG', { weekday: 'long' })}`;
    return formattedDate;
  };

  const handleCompleteReading = (day: number) => {
    setCompletedReadings(prev => {
      const newCompleted = [...prev, day];
      // Update the saved khatma in localStorage
      const savedKhatmasStr = localStorage.getItem('savedKhatmas');
      if (savedKhatmasStr) {
        const savedKhatmas = JSON.parse(savedKhatmasStr);
        const updatedKhatmas = savedKhatmas.map((khatma: SavedKhatma) => {
          if (khatma.duration === selectedKhatmaDuration) {
            return { ...khatma, completedReadings: newCompleted };
          }
          return khatma;
        });
        localStorage.setItem('savedKhatmas', JSON.stringify(updatedKhatmas));
        setSavedKhatmas(updatedKhatmas);
      }
      return newCompleted;
    });
  };

  const handleConfirmKhatma = () => {
    if (selectedKhatmaDuration) {
      const startDate = new Date();
      if (selectedSchedule === 'غداً') {
        startDate.setDate(startDate.getDate() + 1);
      } else if (selectedSchedule === 'بعد غد') {
        startDate.setDate(startDate.getDate() + 2);
      }

      const selectedScheduleDetails = khatmaSchedules.find(s => s.days === selectedKhatmaDuration);
      const newKhatma: SavedKhatma = {
        id: Date.now().toString(),
        duration: selectedKhatmaDuration,
        startDate: startDate.toISOString(),
        completedReadings: [],
        schedule: selectedSchedule,
        name: selectedScheduleDetails?.name || ''
      };

      const updatedKhatmas = [...savedKhatmas, newKhatma];
      localStorage.setItem('savedKhatmas', JSON.stringify(updatedKhatmas));
      setSavedKhatmas(updatedKhatmas);
      setKhatmaView('initial');
    }
  };

  const handleDeleteKhatma = (khatmaId: string) => {
    const updatedKhatmas = savedKhatmas.filter(k => k.id !== khatmaId);
    localStorage.setItem('savedKhatmas', JSON.stringify(updatedKhatmas));
    setSavedKhatmas(updatedKhatmas);
  };

  const handleSelectKhatma = (khatma: SavedKhatma) => {
    setSelectedKhatmaDuration(khatma.duration);
    setCompletedReadings(khatma.completedReadings);
    setSelectedSchedule(khatma.schedule as 'اليوم' | 'غداً' | 'بعد غد');
    
    const startDate = new Date(khatma.startDate);
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    setCurrentDay(Math.max(1, diffDays + 1));
    
    setKhatmaView('dailyReading');
  };

  const khatmaSchedules = [
    { id: 1, name: "ختمة 29 يوما", days: 29, description: "الورد اليومي: جزء تقريباً" },
    { id: 2, name: "ختمة 7 أيام", days: 7, description: "الورد اليومي: 4 أجزاء تقريباً" },
    { id: 3, name: "ختمة 30 يوما", days: 30, description: "الورد اليومي: جزء" },
    { id: 4, name: "ختمة 240 يوما", days: 240, description: "الورد اليومي: ربع" },
    { id: 5, name: "ختمة 120 يوما", days: 120, description: "الورد اليومي: رمضان" },
    { id: 6, name: "ختمة 80 يوما", days: 80, description: "الورد اليومي: 3 أرباع" },
    { id: 7, name: "ختمة 60 يوما", days: 60, description: "الورد اليومي: حزب" },
    { id: 8, name: "ختمة 40 يوما", days: 40, description: "الورد اليومي: حزب ونصف" },
    { id: 9, name: "ختمة 20 يوما", days: 20, description: "الورد اليومي: جزء ونصف" },
    { id: 10, name: "ختمة 15 يوما", days: 15, description: "الورد اليومي: جزءان" },
    { id: 11, name: "ختمة 10 أيام", days: 10, description: "الورد اليومي: 3 أجزاء" },
    { id: 12, name: "ختمة 6 أيام", days: 6, description: "الورد اليومي: 5 أجزاء" },
    { id: 13, name: "ختمة 3 أيام", days: 3, description: "الورد اليومي: 10 أجزاء" }
  ];

  const renderHeader = (title: string, showBackButton: boolean = true, backAction?: () => void) => (
    <div className="bg-gray-900 p-4 flex items-center justify-between">
      {showBackButton ? (
        <button onClick={backAction || onBack} className="text-green-400">
          <ArrowRight size={20} />
        </button>
      ) : (
        <div className="w-6"></div>
      )}
      <h1 className="text-lg font-bold">{title}</h1>
      <div className="w-6"></div>
    </div>
  );

  const renderInitialScreen = () => (
    <div className="min-h-screen bg-gray-900 text-white">
      {renderHeader("الختمة", true, onBack)}
      <div className="flex flex-col items-center py-6 px-4">
        <div className="mb-6">
          <svg width="100" height="100" viewBox="0 0 120 120" fill="none" className="text-green-500">
            <path d="M20 5 L30 15 L30 25 L20 35 L10 25 L10 15 Z" stroke="currentColor" strokeWidth="3" fill="none"/>
            <path d="M15 17 L25 17 M15 20 L25 20 M15 23 L25 23" stroke="currentColor" strokeWidth="2"/>
          </svg>
        </div>
        
        <h2 className="text-white text-xl font-bold mb-6 text-center">
          حدد وردك اليومي أو المدة التي تريد ختم القرآن فيها
        </h2>
        
        {savedKhatmas.length > 0 && (
          <div className="w-full mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-white text-lg font-bold">الختمات المحفوظة</h3>
              <span className="text-gray-400 text-sm">{savedKhatmas.length} ختمة</span>
            </div>
            <div className="space-y-4">
              {savedKhatmas.map((khatma) => (
                <div key={khatma.id} className="bg-gray-800 rounded-xl p-4 shadow-lg border border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => handleDeleteKhatma(khatma.id)}
                        className="text-red-500 hover:text-red-400 p-2 rounded-full hover:bg-red-900/20 transition-colors"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                    <div className="text-right flex-grow mx-4">
                      <h4 className="text-white font-bold text-lg mb-1">{khatma.name}</h4>
                      <p className="text-gray-400 text-sm mb-1">
                        بدأت في {new Date(khatma.startDate).toLocaleDateString('ar-EG', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
                      </p>
                      <div className="flex items-center justify-end space-x-2">
                        <div className="bg-gray-700 rounded-full px-3 py-1 text-sm">
                          <span className="text-green-400">{khatma.completedReadings.length}</span>
                          <span className="text-gray-400"> / </span>
                          <span className="text-white">{khatma.duration}</span>
                        </div>
                        <span className="text-gray-400 text-sm">أوراد مكتملة</span>
                      </div>
                    </div>
                    <button
                      onClick={() => handleSelectKhatma(khatma)}
                      className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
                    >
                      <span>متابعة</span>
                      <ChevronLeft size={20} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <button
          onClick={() => setKhatmaView('selectKhatma')}
          className="bg-green-600 text-white px-8 py-3 rounded-xl hover:bg-green-700 transition-colors flex items-center space-x-2 shadow-lg"
        >
          <span className="text-lg">بدء ختمة جديدة</span>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        </button>
      </div>
    </div>
  );

  const renderSelectKhatmaScreen = () => (
    <div className="min-h-screen bg-gray-900 text-white">
      {renderHeader("اختر ختمة", true, () => setKhatmaView('initial'))}
      <div className="bg-gray-800 p-4">
        <div className="flex justify-around bg-gray-700 rounded-lg p-1 mb-4">
          <button
            onClick={() => setSelectedSchedule('اليوم')}
            className={`px-6 py-2 rounded-lg text-sm ${selectedSchedule === 'اليوم' ? 'bg-green-600 text-white' : 'text-gray-300'}`}
          >
            اليوم
          </button>
          <button
            onClick={() => setSelectedSchedule('غداً')}
            className={`px-6 py-2 rounded-lg text-sm ${selectedSchedule === 'غداً' ? 'bg-green-600 text-white' : 'text-gray-300'}`}
          >
            غداً
          </button>
          <button
            onClick={() => setSelectedSchedule('بعد غد')}
            className={`px-6 py-2 rounded-lg text-sm ${selectedSchedule === 'بعد غد' ? 'bg-green-600 text-white' : 'text-gray-300'}`}
          >
            بعد غد
          </button>
        </div>
        <div className="mb-4">
          <p className="text-gray-400 text-sm mb-2">بدء الختمة من:</p>
          <div className="bg-gray-700 p-3 rounded-lg text-white">
            بداية المصحف
          </div>
        </div>
        <div className="space-y-3">
          <p className="text-gray-400 text-sm mb-2">ورد محدد ...</p>
          {khatmaSchedules.map((schedule) => (
            <div
              key={schedule.id}
              onClick={() => {
                setSelectedKhatmaDuration(schedule.days);
                setKhatmaView('confirmKhatma');
              }}
              className="bg-gray-700 rounded-lg p-4 cursor-pointer hover:bg-gray-600 transition-colors"
            >
              <div className="flex items-center justify-between">
                <ChevronLeft size={20} className="text-gray-400" />
                <div className="text-right">
                  <h4 className="text-white font-bold">{schedule.name}</h4>
                  <p className="text-gray-400 text-sm">{schedule.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderConfirmKhatmaScreen = () => {
    const selectedScheduleDetails = khatmaSchedules.find(s => s.days === selectedKhatmaDuration);
    if (!selectedScheduleDetails) return null;

    const startDate = new Date();
    if (selectedSchedule === 'غداً') {
      startDate.setDate(startDate.getDate() + 1);
    } else if (selectedSchedule === 'بعد غد') {
      startDate.setDate(startDate.getDate() + 2);
    }

    return (
      <div className="min-h-screen bg-gray-900 text-white">
        {renderHeader("تأكيد الختمة", true, () => setKhatmaView('selectKhatma'))}
        <div className="p-4 space-y-4 text-center">
          <h2 className="text-xl font-bold mb-4">هل أنت متأكد من بدء هذه الختمة؟</h2>
          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-white font-bold mb-2">{selectedScheduleDetails.name}</h3>
            <p className="text-gray-400">{selectedScheduleDetails.description}</p>
            <p className="text-gray-400 mt-2">
              سيتم بدء الختمة من {selectedSchedule === 'اليوم' ? 'اليوم' : selectedSchedule === 'غداً' ? 'غداً' : 'بعد غد'} - {startDate.toLocaleDateString('ar-EG', { weekday: 'long' })}
            </p>
          </div>
          <div className="flex justify-around mt-6">
            <button
              onClick={handleConfirmKhatma}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700"
            >
              تأكيد وبدء الختمة
            </button>
            <button
              onClick={() => setKhatmaView('selectKhatma')}
              className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700"
            >
              إلغاء
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderDailyReadingScreen = () => (
    <div className="min-h-screen bg-gray-900 text-white">
      {renderHeader(`ختمة ${selectedKhatmaDuration} يوما`, true, () => setKhatmaView('initial'))}
      <div className="bg-gray-800 p-4">
        <div className="space-y-3">
          {currentDailyReadings.map((reading) => (
            <div
              key={reading.day}
              onClick={() => {
                setSelectedDayReading(reading);
                setCurrentDay(reading.day);
                setKhatmaView('khatmaDetails');
              }}
              className="bg-gray-700 rounded-lg p-4 cursor-pointer hover:bg-gray-600 transition-colors"
            >
              <div className="flex items-center justify-between">
                <ChevronLeft size={20} className="text-gray-400" />
                <div className="text-right">
                  <h4 className="text-white font-bold">ورد اليوم {reading.day}</h4>
                  <p className="text-gray-400 text-sm">
                    {getDisplayDateForReading(reading.day)}
                  </p>
                  <p className="text-gray-400 text-sm">
                    {reading.title} {reading.fromAya ? `من آية ${reading.fromAya}` : ''} {reading.fromPage ? `صفحة ${reading.fromPage}` : ''}
                    {reading.toAya ? ` إلى آية ${reading.toAya}` : ''} {reading.toPage ? `صفحة ${reading.toPage}` : ''}
                  </p>
                </div>
                {completedReadings.includes(reading.day) ? (
                  <Check size={20} className="text-green-400" />
                ) : (
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCompleteReading(reading.day);
                    }}
                    className="bg-green-600 text-white px-3 py-1 rounded-lg text-sm"
                  >
                    إتمام
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderKhatmaDetailsScreen = () => (
    <div className="min-h-screen bg-gray-900 text-white">
      {renderHeader(`ختمة ${selectedKhatmaDuration} يوما`, true, () => setKhatmaView('dailyReading'))}
      <div className="bg-gray-800 px-4 py-3 flex items-center justify-between">
        <span className="text-gray-400">{currentDayReading?.juz}</span>
        <span className="text-white font-bold">ورد اليوم {currentDayReading?.day}</span>
        <div className="flex items-center">
          <button
            onClick={() => {
              setCurrentDay(prev => Math.max(1, prev - 1));
              setSelectedDayReading(currentDailyReadings.find(dr => dr.day === Math.max(1, currentDay - 1)));
            }}
            className="text-gray-400" >
            <ChevronRight size={20} />
          </button>
          <button
            onClick={() => {
              setCurrentDay(prev => Math.min(currentDailyReadings.length, prev + 1));
              setSelectedDayReading(currentDailyReadings.find(dr => dr.day === Math.min(currentDailyReadings.length, currentDay + 1)));
            }}
            className="text-gray-400" >
            <ChevronLeft size={20} />
          </button>
        </div>
      </div>
      <div className="p-4 space-y-4">
        {currentDayReading?.fromAya && currentDayReading?.fromPage && (
          <div className="bg-gray-800 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <span className="text-white">من {currentDayReading.title.startsWith("سورة") ? currentDayReading.title.replace("سورة ", '') : currentDayReading.title}: صفحة {currentDayReading.fromPage}</span>
              <ChevronRight size={20} className="text-gray-400" />
            </div>
            <p className="text-gray-300 text-right leading-relaxed">
              سَيَقُولُ السُّفَهَاءُ مِنَ النَّاسِ مَا وَلَّاهُمْ عَن قِبْلَتِهِمُ الَّتِي كَانُوا عَلَيْهَا...
            </p>
          </div>
        )}
        {currentDayReading?.toAya && currentDayReading?.toPage && (
          <div className="bg-gray-800 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <span className="text-white">إلى {currentDayReading.title.startsWith("من سورة") ? currentDayReading.title.split("إلى سورة ")[1] : currentDayReading.title}: صفحة {currentDayReading.toPage}</span>
              <ChevronRight size={20} className="text-gray-400" />
            </div>
            <p className="text-gray-300 text-right leading-relaxed">
              تِلْكَ آيَاتُ اللَّهِ نَتْلُوهَا عَلَيْكَ بِالْحَقِّ وَإِنَّكَ لَمِنَ الْمُرْسَلِينَ...
            </p>
          </div>
        )}
        <div className="space-y-4">
          <div className="bg-gray-800 rounded-lg p-4 cursor-pointer" onClick={() => setKhatmaView('dailyReading')}>
            <div className="flex items-center justify-between mb-3">
              <span className="text-white">قائمة الأوراد</span>
              <ChevronRight size={20} className="text-gray-400" />
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg p-4 cursor-pointer" onClick={() => setKhatmaView('reminder')}>
            <div className="flex items-center justify-between mb-3">
              <span className="text-white">منبه الختمة 🔔</span>
              <ChevronRight size={20} className="text-gray-400" />
            </div>
          </div>
          <div className="bg-red-700 rounded-lg p-4 cursor-pointer hover:bg-red-600 transition-colors" onClick={() => setKhatmaView('initial')}>
            <div className="flex items-center justify-between mb-3">
              <span className="text-white">حذف الختمة الحالية 🗑️</span>
              <ChevronLeft size={20} className="text-white" />
            </div>
          </div>
        </div>
        <div className="bg-gray-800 rounded-lg p-6 text-center">
          <h3 className="text-white font-bold mb-2">الختمة الحالية</h3>
          <div className="flex justify-between items-center mb-4">
            <span className="text-gray-400">الأوراد السابقة: {currentDay - 1}</span>
            <span className="text-gray-400">أنت على المسار الصحيح</span>
          </div>
          <div className="flex justify-between items-center mb-4">
            <span className="text-gray-400">الأوراد القادمة: {selectedKhatmaDuration ? selectedKhatmaDuration - currentDay : ''}</span>
          </div>
          <div className="bg-gray-700 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-center mb-2">
              <span className="text-green-400 text-lg font-bold">أتممت قراءة الورد {currentDay} {completedReadings.includes(currentDay) ? '✓' : ''}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderReminderScreen = () => {
    return (
      <div className="min-h-screen bg-gray-900 text-white">
        {renderHeader("منبه الختمة", true, () => setKhatmaView('khatmaDetails'))}
        <div className="p-4 space-y-4">
          <div className="bg-gray-800 rounded-lg p-4 flex items-center justify-between">
            <span className="text-white">تفعيل تنبيه الورد اليومي</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" value="" className="sr-only peer" checked={reminderEnabled} onChange={() => setReminderEnabled(!reminderEnabled)} />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"></div>
            </label>
          </div>
          <div className="bg-gray-800 rounded-lg p-4">
            <span className="text-white block mb-2">وقت التنبيه</span>
            <input
              type="time"
              className="bg-gray-700 text-white text-lg rounded-lg block w-full p-2.5 text-center"
              value={reminderTime}
              onChange={(e) => setReminderTime(e.target.value)}
            />
          </div>
        </div>
      </div>
    );
  };

  switch (khatmaView) {
    case 'initial':
      return renderInitialScreen();
    case 'selectKhatma':
      return renderSelectKhatmaScreen();
    case 'dailyReading':
      return renderDailyReadingScreen();
    case 'khatmaDetails':
      return renderKhatmaDetailsScreen();
    case 'reminder':
      return renderReminderScreen();
    case 'confirmKhatma':
      return renderConfirmKhatmaScreen();
    default:
      return renderInitialScreen();
  }
};

export default KhatmaTracker;
