import fs from 'fs';

console.log('📝 تحديث صفحات السور في surah.json حسب المصحف الدقيق...\n');

// قراءة البيانات الحالية
const surahData = JSON.parse(fs.readFileSync('./src/data/surah.json', 'utf8'));

// الصفحات الدقيقة لكل سورة حسب مصحف المدينة النبوية
const accuratePages = {
  1: 1, 2: 2, 3: 50, 4: 77, 5: 106, 6: 128, 7: 151, 8: 177, 9: 187, 10: 208,
  11: 221, 12: 235, 13: 249, 14: 255, 15: 262, 16: 267, 17: 282, 18: 293, 19: 305, 20: 312,
  21: 322, 22: 332, 23: 342, 24: 350, 25: 359, 26: 367, 27: 377, 28: 385, 29: 396, 30: 404,
  31: 411, 32: 415, 33: 418, 34: 428, 35: 434, 36: 440, 37: 446, 38: 453, 39: 458, 40: 467,
  41: 477, 42: 483, 43: 489, 44: 496, 45: 499, 46: 502, 47: 507, 48: 511, 49: 515, 50: 518,
  51: 520, 52: 523, 53: 526, 54: 528, 55: 531, 56: 534, 57: 537, 58: 542, 59: 545, 60: 549,
  61: 551, 62: 553, 63: 554, 64: 556, 65: 558, 66: 560, 67: 562, 68: 564, 69: 566, 70: 568,
  71: 570, 72: 572, 73: 574, 74: 575, 75: 577, 76: 578, 77: 580, 78: 582, 79: 583, 80: 585,
  81: 586, 82: 587, 83: 587, 84: 589, 85: 590, 86: 591, 87: 591, 88: 592, 89: 593, 90: 594,
  91: 595, 92: 595, 93: 596, 94: 596, 95: 597, 96: 597, 97: 598, 98: 598, 99: 599, 100: 599,
  101: 600, 102: 600, 103: 601, 104: 601, 105: 602, 106: 602, 107: 603, 108: 603, 109: 603, 110: 604,
  111: 604, 112: 604, 113: 604, 114: 604
};

// تحديث البيانات
const updatedSurahData = surahData.map(surah => {
  const surahId = parseInt(surah.index);
  const correctPage = accuratePages[surahId];
  
  return {
    ...surah,
    pages: correctPage.toString()
  };
});

// حفظ البيانات المحدثة
fs.writeFileSync('./src/data/surah.json', JSON.stringify(updatedSurahData, null, 4), 'utf8');

console.log('✅ تم تحديث جميع صفحات السور في surah.json');

// التحقق من بعض السور المهمة
console.log('\n🔍 التحقق من السور المهمة:');
const importantSurahs = [1, 78, 79, 112, 113, 114];

importantSurahs.forEach(surahId => {
  const surah = updatedSurahData.find(s => parseInt(s.index) === surahId);
  if (surah) {
    console.log(`سورة ${surah.titleAr} (${surahId}): صفحة ${surah.pages}`);
  }
});

console.log('\n✅ تم تحديث جميع البيانات بدقة!');
