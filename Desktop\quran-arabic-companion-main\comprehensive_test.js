import fs from 'fs';

console.log('🧪 اختبار شامل للتأكد من دقة جميع البيانات...\n');

// قراءة جميع البيانات
const surahData = JSON.parse(fs.readFileSync('./src/data/surah.json', 'utf8'));
const juzData = JSON.parse(fs.readFileSync('./src/data/juz.json', 'utf8'));
const pagesData = JSON.parse(fs.readFileSync('./src/data/pages.json', 'utf8'));

console.log('1️⃣ اختبار تطابق البيانات:');

// اختبار سورة النبأ (78)
console.log('\n🔍 اختبار سورة النبأ (78):');
const surahNaba = surahData.find(s => parseInt(s.index) === 78);
console.log(`- في surah.json: صفحة ${surahNaba.pages}`);

// البحث في pages.json
const pageWithNaba = pagesData.find(p => p.surahs.some(s => s.surah === 78));
console.log(`- في pages.json: صفحة ${pageWithNaba.page}`);

// قراءة ملف السورة للتحقق من المحتوى
try {
  const nabaContent = JSON.parse(fs.readFileSync('./src/data/surah/surah_78.json', 'utf8'));
  console.log(`- عدد الآيات في الملف: ${nabaContent.count}`);
  console.log(`- الآية الأولى: ${nabaContent.verse.verse_1.substring(0, 50)}...`);
  console.log(`- الآية الأخيرة: ${nabaContent.verse.verse_40.substring(0, 50)}...`);
} catch (error) {
  console.log(`❌ خطأ في قراءة ملف سورة النبأ: ${error.message}`);
}

// اختبار سورة النازعات (79)
console.log('\n🔍 اختبار سورة النازعات (79):');
const surahNaziat = surahData.find(s => parseInt(s.index) === 79);
console.log(`- في surah.json: صفحة ${surahNaziat.pages}`);

const pageWithNaziat = pagesData.find(p => p.surahs.some(s => s.surah === 79));
console.log(`- في pages.json: صفحة ${pageWithNaziat.page}`);

try {
  const naziatContent = JSON.parse(fs.readFileSync('./src/data/surah/surah_79.json', 'utf8'));
  console.log(`- عدد الآيات في الملف: ${naziatContent.count}`);
  console.log(`- الآية الأولى: ${naziatContent.verse.verse_1.substring(0, 50)}...`);
  console.log(`- الآية الأخيرة: ${naziatContent.verse.verse_46.substring(0, 50)}...`);
} catch (error) {
  console.log(`❌ خطأ في قراءة ملف سورة النازعات: ${error.message}`);
}

// اختبار سورة الناس (114)
console.log('\n🔍 اختبار سورة الناس (114):');
const surahNas = surahData.find(s => parseInt(s.index) === 114);
console.log(`- في surah.json: صفحة ${surahNas.pages}`);

const pageWithNas = pagesData.find(p => p.surahs.some(s => s.surah === 114));
console.log(`- في pages.json: صفحة ${pageWithNas.page}`);

try {
  const nasContent = JSON.parse(fs.readFileSync('./src/data/surah/surah_114.json', 'utf8'));
  console.log(`- عدد الآيات في الملف: ${nasContent.count}`);
  console.log(`- جميع الآيات:`);
  for (let i = 1; i <= 6; i++) {
    console.log(`  آية ${i}: ${nasContent.verse[`verse_${i}`]}`);
  }
} catch (error) {
  console.log(`❌ خطأ في قراءة ملف سورة الناس: ${error.message}`);
}

console.log('\n2️⃣ اختبار الأجزاء:');

// اختبار الجزء الرابع
const juz4 = juzData[3];
console.log(`الجزء الرابع: يبدأ من صفحة ${juz4.startPage}`);

const page62 = pagesData[61]; // الصفحة 62
console.log(`الصفحة 62: الجزء ${page62.juz}, السور: ${page62.surahs.map(s => s.name).join(', ')}`);

// اختبار الجزء الثلاثين
const juz30 = juzData[29];
console.log(`الجزء الثلاثون: يبدأ من صفحة ${juz30.startPage}`);

const page582 = pagesData[581]; // الصفحة 582
console.log(`الصفحة 582: الجزء ${page582.juz}, السور: ${page582.surahs.map(s => s.name).join(', ')}`);

console.log('\n3️⃣ اختبار شمولية البيانات:');

// التحقق من أن جميع الصفحات موجودة
if (pagesData.length === 604) {
  console.log('✅ جميع الصفحات موجودة (604 صفحة)');
} else {
  console.log(`❌ عدد الصفحات خاطئ: ${pagesData.length} بدلاً من 604`);
}

// التحقق من أن جميع السور موجودة في الصفحات
const surahsInPages = new Set();
pagesData.forEach(page => {
  page.surahs.forEach(surah => {
    surahsInPages.add(surah.surah);
  });
});

if (surahsInPages.size === 114) {
  console.log('✅ جميع السور موجودة في الصفحات (1-114)');
} else {
  console.log(`❌ عدد السور في الصفحات: ${surahsInPages.size} بدلاً من 114`);
  
  // البحث عن السور المفقودة
  const missingSurahs = [];
  for (let i = 1; i <= 114; i++) {
    if (!surahsInPages.has(i)) {
      missingSurahs.push(i);
    }
  }
  if (missingSurahs.length > 0) {
    console.log(`السور المفقودة: ${missingSurahs.join(', ')}`);
  }
}

console.log('\n4️⃣ اختبار صحة المحتوى:');

// اختبار عينة من السور للتأكد من وجود المحتوى
const testSurahs = [1, 2, 18, 36, 78, 79, 112, 113, 114];
let contentErrors = 0;

testSurahs.forEach(surahId => {
  try {
    const surahContent = JSON.parse(fs.readFileSync(`./src/data/surah/surah_${surahId}.json`, 'utf8'));
    const expectedCount = surahContent.count;
    
    // التحقق من وجود جميع الآيات
    let missingVerses = 0;
    for (let v = 1; v <= expectedCount; v++) {
      if (!surahContent.verse[`verse_${v}`] || surahContent.verse[`verse_${v}`].trim() === '') {
        missingVerses++;
      }
    }
    
    if (missingVerses === 0) {
      console.log(`✅ السورة ${surahId}: جميع الآيات موجودة (${expectedCount} آية)`);
    } else {
      console.log(`❌ السورة ${surahId}: ${missingVerses} آية مفقودة من أصل ${expectedCount}`);
      contentErrors++;
    }
    
  } catch (error) {
    console.log(`❌ السورة ${surahId}: خطأ في القراءة - ${error.message}`);
    contentErrors++;
  }
});

console.log('\n📊 ملخص النتائج:');

if (contentErrors === 0) {
  console.log('🎉 ممتاز! جميع البيانات صحيحة ومكتملة');
  console.log('✅ السور: 114 سورة كاملة');
  console.log('✅ الصفحات: 604 صفحة دقيقة');
  console.log('✅ الأجزاء: 30 جزء بصفحات صحيحة');
  console.log('✅ المحتوى: جميع الآيات موجودة');
} else {
  console.log(`❌ توجد ${contentErrors} مشكلة في المحتوى`);
}

console.log('\n🚀 التطبيق جاهز للاختبار النهائي!');
