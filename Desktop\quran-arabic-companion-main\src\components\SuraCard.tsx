interface SuraCardProps {
  id: number;
  name: string;
  arabicName: string;
  verses: number;
  type: string;
  page: number;
  onClick: () => void;
}

const SuraCard = ({ id, name, arabicName, verses, type, page, onClick }: SuraCardProps) => {
  return (
    <div
      onClick={onClick}
      className="bg-gray-800 rounded-lg p-4 cursor-pointer hover:bg-gray-700 transition-colors"
    >
      <div className="flex items-center justify-between">
        <div className="bg-green-600 text-white rounded-lg w-10 h-10 flex items-center justify-center font-bold">
          {id}
        </div>
        <div className="flex-1 text-right mr-3">
          <h3 className="text-white font-bold text-lg mb-1">سورة {arabicName}</h3>
          <p className="text-gray-400 text-sm">
            {type === 'Medina' ? 'مدنية' : 'مكية'}, آياتها: {verses}
          </p>
          <p className="text-gray-500 text-sm">الصفحة {page}</p>
        </div>
      </div>
    </div>
  );
};

export default SuraCard;
