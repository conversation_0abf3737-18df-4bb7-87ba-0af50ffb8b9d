import React from 'react';
import { 
  SurahHeaderOrnament, 
  BismillahOrnament, 
  VerseNumberOrnament, 
  QuranPageOrnament,
  SectionDivider 
} from './IslamicOrnaments';

interface Verse {
  number: number;
  text: string;
}

interface QuranPageWithOrnamentsProps {
  surahName: string;
  surahNameEnglish: string;
  surahNumber: number;
  verses: Verse[];
  showBismillah?: boolean;
  pageNumber?: number;
}

const QuranPageWithOrnaments: React.FC<QuranPageWithOrnamentsProps> = ({
  surahName,
  surahNameEnglish,
  surahNumber,
  verses,
  showBismillah = true,
  pageNumber
}) => {
  return (
    <QuranPageOrnament className="max-w-4xl mx-auto my-8">
      {/* Surah Header with Islamic Frame */}
      <SurahHeaderOrnament
        surahNameArabic={surahName}
        surahNameEnglish={surahNameEnglish}
        surahNumber={surahNumber}
      />

      {/* Section Divider */}
      <SectionDivider />

      {/* B<PERSON>illah (if needed) */}
      {showBismillah && surahNumber !== 1 && surahNumber !== 9 && (
        <BismillahOrnament />
      )}

      {/* Verses with ornamental numbers */}
      <div className="verses-container space-y-6">
        {verses.map((verse) => (
          <div key={verse.number} className="verse-row flex items-start gap-4">
            <div className="verse-content flex-1">
              <p className="arabic-text text-right text-2xl leading-loose font-arabic">
                {verse.text}
                <VerseNumberOrnament number={verse.number} />
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Page Number (if provided) */}
      {pageNumber && (
        <div className="page-number-container mt-8 text-center">
          <SectionDivider />
          <div className="page-number text-lg font-semibold text-gray-600 mt-4">
            صفحة {pageNumber}
          </div>
        </div>
      )}
    </QuranPageOrnament>
  );
};

export default QuranPageWithOrnaments;
