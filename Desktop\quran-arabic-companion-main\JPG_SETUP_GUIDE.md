# 📸 دليل استخدام ملفات JPG - JPG Setup Guide

## 🎯 خطوات سريعة للتطبيق

### 1️⃣ **نقل الصور**
```bash
# انسخ الصورتين إلى:
public/assets/images/islamic-frame-1.jpg      # الإطار الذهبي المقوس
public/assets/images/islamic-page-border.jpg  # إطار الصفحة التقليدي
```

### 2️⃣ **الاستخدام المباشر**
```tsx
import { SimpleSurahHeader, SimpleQuranPage } from './components/SimpleImageOrnaments';

// رأس السورة
<SimpleSurahHeader 
  surahNameArabic="الفاتحة" 
  surahNameEnglish="Al-Fatiha" 
  surahNumber={1} 
/>

// صفحة القرآن
<SimpleQuranPage>
  <div className="bismillah-in-frame">
    بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
  </div>
  <div className="verses-in-frame">
    الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ
    <span className="verse-number-in-frame">1</span>
  </div>
</SimpleQuranPage>
```

### 3️⃣ **استخدام كخلفية CSS**
```css
.my-header {
  background-image: url('/assets/images/islamic-frame-1.jpg');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.my-page {
  background-image: url('/assets/images/islamic-page-border.jpg');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
```

### 4️⃣ **استخدام كعنصر img**
```tsx
<img 
  src="/assets/images/islamic-frame-1.jpg" 
  alt="Islamic Frame"
  className="w-96 h-auto"
/>

<img 
  src="/assets/images/islamic-page-border.jpg" 
  alt="Page Border"
  className="w-full h-auto"
/>
```

## 🎨 **مثال شامل**

```tsx
import React from 'react';

const MyQuranPage = () => {
  return (
    <div className="p-8">
      {/* رأس السورة بالصورة الأولى */}
      <div className="surah-header-original">
        <div className="content">
          <div className="surah-name-arabic">البقرة</div>
          <div className="surah-name-english">Al-Baqarah • 2</div>
        </div>
      </div>
      
      {/* محتوى الصفحة بالصورة الثانية */}
      <div className="quran-page-original">
        <div className="page-content">
          <div className="bismillah-in-frame">
            بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
          </div>
          
          <div className="verses-in-frame">
            <div className="verse-in-frame">
              الم <span className="verse-number-in-frame">1</span>
            </div>
            <div className="verse-in-frame">
              ذَٰلِكَ الْكِتَابُ لَا رَيْبَ فِيهِ هُدًى لِّلْمُتَّقِينَ
              <span className="verse-number-in-frame">2</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
```

## 📱 **التصميم المتجاوب**

الكود محسن تلقائياً للهواتف:
- الصور تتكيف مع حجم الشاشة
- النصوص تصغر على الشاشات الصغيرة
- التخطيط يتغير للهواتف المحمولة

## ⚡ **نصائح للأداء**

1. **ضغط الصور**: استخدم أدوات ضغط JPG لتقليل الحجم
2. **الأحجام المناسبة**: 
   - الإطار الذهبي: 400×200 بكسل مثالي
   - إطار الصفحة: 800×600 بكسل مثالي
3. **التحميل السريع**: الصور ستحمل تلقائياً عند الحاجة

## 🔧 **تخصيص الألوان**

```css
/* تخصيص لون النص فوق الصور */
.surah-name-arabic {
  color: #2c3e50; /* يمكن تغييره */
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.verse-number-in-frame {
  background: linear-gradient(135deg, #FFD700 0%, #DAA520 100%);
  /* يمكن تغيير الألوان الذهبية */
}
```

## 🎯 **الملفات المطلوبة**

تأكد من وجود هذه الملفات:
```
public/
└── assets/
    └── images/
        ├── islamic-frame-1.jpg      ← الصورة الأولى (الإطار المقوس)
        └── islamic-page-border.jpg  ← الصورة الثانية (إطار الصفحة)
```

## ✅ **اختبار سريع**

بعد نسخ الصور، اختبر بهذا الكود:

```tsx
// في أي مكون
<div style={{
  backgroundImage: 'url(/assets/images/islamic-frame-1.jpg)',
  backgroundSize: 'contain',
  backgroundRepeat: 'no-repeat',
  width: '400px',
  height: '200px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
}}>
  <h2>اختبار الصورة</h2>
</div>
```

## 🆘 **حل المشاكل**

**إذا لم تظهر الصور:**
1. تأكد من المسار: `public/assets/images/`
2. تأكد من أسماء الملفات: `.jpg` وليس `.jpeg`
3. تأكد من أن الخادم يعمل: `npm run dev`
4. امسح الكاش: Ctrl+F5

---

**🎉 الآن يمكنك استخدام صورك الأصلية بالضبط كما هي!**
