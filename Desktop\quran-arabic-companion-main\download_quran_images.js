// سكريبت لتحميل صور مصحف المدينة النبوية من المصادر الرسمية

import fs from 'fs';
import path from 'path';

console.log('📥 دليل تحميل صور مصحف المدينة النبوية');
console.log('🕌 من المصادر الرسمية الموثوقة\n');

// إنشاء مجلد الصور
const imagesDir = './public/images';
if (!fs.existsSync('./public')) {
  fs.mkdirSync('./public');
}
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir);
}

console.log('📋 المصادر الموصى بها:\n');

console.log('1️⃣ Archive.org (الأسهل والأفضل):');
console.log('   🔗 الرابط: https://archive.org/details/almadinah_4694694679469469');
console.log('   📥 اضغط على "SINGLE PAGE PROCESSED JP2 ZIP" (357.5MB)');
console.log('   📁 استخرج الملفات إلى مجلد public/images/');
console.log('   🔄 أعد تسمية الملفات إلى: page_1.jpg, page_2.jpg, ..., page_604.jpg\n');

console.log('2️⃣ QuranFlash.com (تفاعلي):');
console.log('   🔗 الرابط: https://app.quranflash.com/book/Medina1?ar');
console.log('   💡 يحتاج أدوات تقنية لاستخراج الصور\n');

console.log('3️⃣ مجمع الملك فهد (رسمي):');
console.log('   🔗 الرابط: https://qurancomplex.gov.sa/');
console.log('   📖 المصدر الرسمي الأكثر دقة\n');

// دالة لتحويل أسماء الملفات
function renameFiles() {
  console.log('🔄 تحويل أسماء الملفات...');
  
  const files = fs.readdirSync(imagesDir);
  let renamedCount = 0;
  
  files.forEach((file, index) => {
    if (file.match(/\.(jpg|jpeg|png|jp2)$/i)) {
      const oldPath = path.join(imagesDir, file);
      const newPath = path.join(imagesDir, `page_${index + 1}.jpg`);
      
      try {
        fs.renameSync(oldPath, newPath);
        renamedCount++;
      } catch (error) {
        console.log(`❌ خطأ في إعادة تسمية ${file}: ${error.message}`);
      }
    }
  });
  
  console.log(`✅ تم إعادة تسمية ${renamedCount} ملف`);
}

// دالة للتحقق من الصور
function verifyImages() {
  console.log('\n🔍 التحقق من الصور المحملة:');
  
  const expectedPages = 604;
  let foundPages = 0;
  const missingPages = [];
  
  for (let i = 1; i <= expectedPages; i++) {
    const imagePath = path.join(imagesDir, `page_${i}.jpg`);
    if (fs.existsSync(imagePath)) {
      foundPages++;
    } else {
      missingPages.push(i);
    }
  }
  
  console.log(`📊 الصور الموجودة: ${foundPages}/${expectedPages}`);
  
  if (missingPages.length > 0) {
    console.log(`❌ الصور المفقودة: ${missingPages.slice(0, 10).join(', ')}${missingPages.length > 10 ? '...' : ''}`);
  } else {
    console.log('✅ جميع الصور موجودة!');
  }
  
  return foundPages === expectedPages;
}

// دالة لإنشاء ملف فهرس الصور
function createImageIndex() {
  console.log('\n📝 إنشاء فهرس الصور...');
  
  const imageIndex = {};
  
  for (let i = 1; i <= 604; i++) {
    const imagePath = `/images/page_${i}.jpg`;
    imageIndex[i] = {
      url: imagePath,
      alt: `صفحة ${i} من مصحف المدينة النبوية`,
      page: i
    };
  }
  
  fs.writeFileSync('./src/data/imageIndex.json', JSON.stringify(imageIndex, null, 2));
  console.log('✅ تم إنشاء فهرس الصور في src/data/imageIndex.json');
}

// دالة لتحسين الصور (اختيارية)
function optimizeImages() {
  console.log('\n🎨 تحسين الصور (اختياري):');
  console.log('💡 يمكنك استخدام أدوات مثل:');
  console.log('   - ImageMagick: convert input.jpg -quality 85 -resize 1200x1600 output.jpg');
  console.log('   - TinyPNG: https://tinypng.com/');
  console.log('   - Squoosh: https://squoosh.app/');
}

// دالة لإنشاء صور تجريبية (للاختبار)
function createTestImages() {
  console.log('\n🧪 إنشاء صور تجريبية للاختبار...');
  
  // إنشاء صور SVG بسيطة للاختبار
  for (let i = 1; i <= 10; i++) {
    const svgContent = `
      <svg width="800" height="1200" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f8f9fa"/>
        <rect x="50" y="50" width="700" height="1100" fill="white" stroke="#ddd"/>
        <text x="400" y="100" text-anchor="middle" font-size="24" fill="#333">
          مصحف المدينة النبوية
        </text>
        <text x="400" y="150" text-anchor="middle" font-size="48" fill="#666">
          صفحة ${i}
        </text>
        <text x="400" y="200" text-anchor="middle" font-size="16" fill="#999">
          (صورة تجريبية)
        </text>
      </svg>
    `;
    
    fs.writeFileSync(path.join(imagesDir, `page_${i}.svg`), svgContent);
  }
  
  console.log('✅ تم إنشاء 10 صور تجريبية للاختبار');
}

// تشغيل الوظائف
console.log('🎯 الخيارات المتاحة:');
console.log('1. تحميل الصور من المصادر المذكورة أعلاه');
console.log('2. تشغيل: node download_quran_images.js rename (لإعادة تسمية الملفات)');
console.log('3. تشغيل: node download_quran_images.js verify (للتحقق من الصور)');
console.log('4. تشغيل: node download_quran_images.js test (لإنشاء صور تجريبية)');

// معالجة المعاملات
const command = process.argv[2];

switch (command) {
  case 'rename':
    renameFiles();
    break;
  case 'verify':
    verifyImages();
    break;
  case 'test':
    createTestImages();
    createImageIndex();
    break;
  case 'index':
    createImageIndex();
    break;
  default:
    console.log('\n📖 تعليمات التحميل:');
    console.log('1. اذهب إلى: https://archive.org/details/almadinah_4694694679469469');
    console.log('2. اضغط على "DOWNLOAD OPTIONS"');
    console.log('3. اختر "SINGLE PAGE PROCESSED JP2 ZIP"');
    console.log('4. استخرج الملفات إلى public/images/');
    console.log('5. شغل: node download_quran_images.js rename');
    console.log('6. شغل: node download_quran_images.js verify');
    console.log('7. شغل: node download_quran_images.js index');
    
    console.log('\n🧪 للاختبار السريع:');
    console.log('شغل: node download_quran_images.js test');
}

console.log('\n🎉 النظام الهجين جاهز لاستقبال الصور!');
