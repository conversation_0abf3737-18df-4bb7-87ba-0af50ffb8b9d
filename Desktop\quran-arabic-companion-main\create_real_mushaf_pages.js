import fs from 'fs';

console.log('📖 إنشاء ملف pages.json حسب المصحف الحقيقي (مصحف المدينة النبوية)...\n');

// قراءة أسماء السور
const surahData = JSON.parse(fs.readFileSync('./src/data/surah.json', 'utf8'));
const surahNames = {};
surahData.forEach(surah => {
  surahNames[parseInt(surah.index)] = surah.titleAr;
});

// خريطة دقيقة للجزء الثلاثين (الصفحات 582-604) حسب المصحف الحقيقي
const juz30Pages = {
  582: [
    { surah: 77, name: "المرسلات", verses: [41, 42, 43, 44, 45, 46, 47, 48, 49, 50] }, // نهاية المرسلات
    { surah: 78, name: "الن<PERSON><PERSON>", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20] }
  ],
  583: [
    { surah: 78, name: "النبأ", verses: [21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40] },
    { surah: 79, name: "النازعات", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15] }
  ],
  584: [
    { surah: 79, name: "النازعات", verses: [16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46] }
  ],
  585: [
    { surah: 80, name: "عبس", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42] }
  ],
  586: [
    { surah: 81, name: "التكوير", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29] }
  ],
  587: [
    { surah: 82, name: "الانفطار", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19] },
    { surah: 83, name: "المطففين", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18] }
  ],
  588: [
    { surah: 83, name: "المطففين", verses: [19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36] }
  ],
  589: [
    { surah: 84, name: "الانشقاق", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25] }
  ],
  590: [
    { surah: 85, name: "البروج", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22] }
  ],
  591: [
    { surah: 86, name: "الطارق", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17] },
    { surah: 87, name: "الأعلى", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15] }
  ],
  592: [
    { surah: 87, name: "الأعلى", verses: [16, 17, 18, 19] },
    { surah: 88, name: "الغاشية", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26] }
  ],
  593: [
    { surah: 89, name: "الفجر", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30] }
  ],
  594: [
    { surah: 90, name: "البلد", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20] }
  ],
  595: [
    { surah: 91, name: "الشمس", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15] },
    { surah: 92, name: "الليل", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15] }
  ],
  596: [
    { surah: 92, name: "الليل", verses: [16, 17, 18, 19, 20, 21] },
    { surah: 93, name: "الضحى", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11] },
    { surah: 94, name: "الشرح", verses: [1, 2, 3, 4, 5, 6, 7, 8] }
  ],
  597: [
    { surah: 95, name: "التين", verses: [1, 2, 3, 4, 5, 6, 7, 8] },
    { surah: 96, name: "العلق", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19] }
  ],
  598: [
    { surah: 97, name: "القدر", verses: [1, 2, 3, 4, 5] },
    { surah: 98, name: "البينة", verses: [1, 2, 3, 4, 5, 6, 7, 8] }
  ],
  599: [
    { surah: 99, name: "الزلزلة", verses: [1, 2, 3, 4, 5, 6, 7, 8] },
    { surah: 100, name: "العاديات", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11] }
  ],
  600: [
    { surah: 101, name: "القارعة", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11] },
    { surah: 102, name: "التكاثر", verses: [1, 2, 3, 4, 5, 6, 7, 8] }
  ],
  601: [
    { surah: 103, name: "العصر", verses: [1, 2, 3] },
    { surah: 104, name: "الهمزة", verses: [1, 2, 3, 4, 5, 6, 7, 8, 9] }
  ],
  602: [
    { surah: 105, name: "الفيل", verses: [1, 2, 3, 4, 5] },
    { surah: 106, name: "قريش", verses: [1, 2, 3, 4] }
  ],
  603: [
    { surah: 107, name: "الماعون", verses: [1, 2, 3, 4, 5, 6, 7] },
    { surah: 108, name: "الكوثر", verses: [1, 2, 3] },
    { surah: 109, name: "الكافرون", verses: [1, 2, 3, 4, 5, 6] }
  ],
  604: [
    { surah: 110, name: "النصر", verses: [1, 2, 3] },
    { surah: 111, name: "المسد", verses: [1, 2, 3, 4, 5] },
    { surah: 112, name: "الإخلاص", verses: [1, 2, 3, 4] },
    { surah: 113, name: "الفلق", verses: [1, 2, 3, 4, 5] },
    { surah: 114, name: "الناس", verses: [1, 2, 3, 4, 5, 6] }
  ]
};

// إنشاء صفحات مبسطة للاختبار (سنركز على الجزء 30 أولاً)
const pages = [];

// إضافة صفحات أساسية (1-581)
for (let page = 1; page <= 581; page++) {
  let juzNumber = Math.ceil(page / 20); // تقريبي
  if (juzNumber > 29) juzNumber = 29;
  
  // صفحات مبسطة للاختبار
  if (page === 1) {
    pages.push({
      page: 1,
      juz: 1,
      surahs: [{ surah: 1, name: "الفاتحة", verses: [1, 2, 3, 4, 5, 6, 7] }]
    });
  } else if (page >= 2 && page <= 49) {
    // سورة البقرة
    const versesPerPage = Math.ceil(286 / 48);
    const startVerse = (page - 2) * versesPerPage + 1;
    const endVerse = Math.min(startVerse + versesPerPage - 1, 286);
    const verses = [];
    for (let v = startVerse; v <= endVerse; v++) {
      verses.push(v);
    }
    pages.push({
      page: page,
      juz: juzNumber,
      surahs: [{ surah: 2, name: "البقرة", verses: verses }]
    });
  } else {
    // صفحات أخرى - مبسطة للاختبار
    pages.push({
      page: page,
      juz: juzNumber,
      surahs: [{ surah: Math.ceil(page / 5), name: "سورة تجريبية", verses: [1, 2, 3] }]
    });
  }
}

// إضافة الجزء الثلاثين (الصفحات 582-604) بدقة
for (let page = 582; page <= 604; page++) {
  const pageSurahs = juz30Pages[page] || [];
  pages.push({
    page: page,
    juz: 30,
    surahs: pageSurahs
  });
}

// حفظ الملف
fs.writeFileSync('./src/data/pages.json', JSON.stringify(pages, null, 2), 'utf8');

console.log(`✅ تم إنشاء ملف pages.json مع ${pages.length} صفحة`);
console.log('\n🔍 التحقق من الصفحات المهمة:');
console.log(`الصفحة 582: ${pages[581].surahs.map(s => `${s.name} (${s.verses.length} آية)`).join(', ')}`);
console.log(`الصفحة 583: ${pages[582].surahs.map(s => `${s.name} (${s.verses.length} آية)`).join(', ')}`);
console.log(`الصفحة 604: ${pages[603].surahs.map(s => `${s.name} (${s.verses.length} آية)`).join(', ')}`);

console.log('\n✅ تم إنشاء ملف pages.json دقيق للجزء الثلاثين!');
