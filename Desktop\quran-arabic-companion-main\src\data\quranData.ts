import surahsData from "./surah.json";
import juzsData from "./juz.json";
import quranFullData from "./quran.json";

// تحويل بيانات السور للشكل المطلوب
export const suras = surahsData.map((surah, index) => ({
  id: parseInt(surah.index),
  name: surah.title,
  arabicName: surah.titleAr,
  verses: surah.count,
  type: surah.type === 'Makkiyah' ? 'meccan' : 'medinan',
  page: parseInt(surah.pages)
}));

// تحويل بيانات الأجزاء للشكل المطلوب
export const ajzaa = juzsData.map((juz, index) => ({
  id: index + 1,
  name: `الجزء ${index + 1}`,
  page: juz.startPage || 1 // استخدام الصفحة المحدثة من الملف
}));

export const quranData = quranFullData;
