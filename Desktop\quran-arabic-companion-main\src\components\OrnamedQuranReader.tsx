import React, { useState } from 'react';
import { 
  SurahHeaderOrnament, 
  BismillahOrnament, 
  VerseNumberOrnament, 
  QuranPageOrnament,
  SectionDivider 
} from './IslamicOrnaments';

interface Verse {
  number: number;
  text: string;
  translation?: string;
}

interface Surah {
  number: number;
  name: string;
  englishName: string;
  verses: Verse[];
  revelationType: 'Meccan' | 'Medinan';
}

interface OrnamedQuranReaderProps {
  surah: Surah;
  showTranslation?: boolean;
  showBismillah?: boolean;
  pageNumber?: number;
  onVerseClick?: (verseNumber: number) => void;
}

const OrnamedQuranReader: React.FC<OrnamedQuranReaderProps> = ({
  surah,
  showTranslation = false,
  showBismillah = true,
  pageNumber,
  onVerseClick
}) => {
  const [selectedVerse, setSelectedVerse] = useState<number | null>(null);

  const handleVerseClick = (verseNumber: number) => {
    setSelectedVerse(verseNumber === selectedVerse ? null : verseNumber);
    onVerseClick?.(verseNumber);
  };

  return (
    <QuranPageOrnament className="max-w-5xl mx-auto my-8">
      {/* Ornamental Surah Header */}
      <SurahHeaderOrnament
        surahNameArabic={surah.name}
        surahNameEnglish={surah.englishName}
        surahNumber={surah.number}
      />

      {/* Section Divider */}
      <SectionDivider />

      {/* Bismillah with Ornament (skip for Al-Fatiha and At-Tawbah) */}
      {showBismillah && surah.number !== 1 && surah.number !== 9 && (
        <>
          <BismillahOrnament />
          <SectionDivider />
        </>
      )}

      {/* Verses Container */}
      <div className="verses-container space-y-8 px-4">
        {surah.verses.map((verse) => (
          <div 
            key={verse.number} 
            className={`verse-row group transition-all duration-300 ${
              selectedVerse === verse.number 
                ? 'bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 shadow-lg' 
                : 'hover:bg-gray-50 rounded-lg p-2'
            }`}
            onClick={() => handleVerseClick(verse.number)}
          >
            {/* Arabic Text with Ornamental Verse Number */}
            <div className="arabic-verse-container mb-4">
              <p className="arabic-text text-right text-3xl leading-loose font-arabic text-gray-800 cursor-pointer">
                {verse.text}
                <VerseNumberOrnament 
                  number={verse.number} 
                  className="mx-2 hover:scale-110 transition-transform duration-200" 
                />
              </p>
            </div>

            {/* Translation (if enabled) */}
            {showTranslation && verse.translation && (
              <div className="translation-container mt-4 pt-4 border-t border-gray-200">
                <p className="text-left text-lg leading-relaxed text-gray-600 font-sans">
                  {verse.translation}
                </p>
              </div>
            )}

            {/* Verse Actions (shown when selected) */}
            {selectedVerse === verse.number && (
              <div className="verse-actions mt-4 pt-4 border-t border-gray-300">
                <div className="flex flex-wrap gap-2 justify-center">
                  <button className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                    📖 حفظ الآية
                  </button>
                  <button className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                    📤 مشاركة
                  </button>
                  <button className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                    🔖 إضافة علامة
                  </button>
                  <button className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors">
                    🎵 تشغيل
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Page Footer with Page Number */}
      {pageNumber && (
        <div className="page-footer mt-12 pt-8">
          <SectionDivider />
          <div className="text-center mt-6">
            <div className="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-amber-100 to-yellow-100 rounded-full border-2 border-amber-300 shadow-lg">
              <span className="text-xl font-bold text-amber-800">
                صفحة {pageNumber}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Surah Info Footer */}
      <div className="surah-info-footer mt-8 pt-6 border-t border-gray-200">
        <div className="text-center text-gray-600">
          <p className="text-lg font-semibold mb-2">
            {surah.englishName} • {surah.name}
          </p>
          <p className="text-sm">
            {surah.revelationType === 'Meccan' ? 'مكية' : 'مدنية'} • 
            {surah.verses.length} آية • 
            السورة رقم {surah.number}
          </p>
        </div>
      </div>
    </QuranPageOrnament>
  );
};

export default OrnamedQuranReader;
