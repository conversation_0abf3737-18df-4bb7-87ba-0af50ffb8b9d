import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  root: './', // Add this line
  base: './',
  server: {
    // host: true, // Removed
    // open: true, // Removed
    // cors: true, // Removed
  },
  plugins: [
    react(),
    // mode === 'development' && // Removed
    // componentTagger(), // Removed
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
