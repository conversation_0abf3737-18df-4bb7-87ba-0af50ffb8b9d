import fs from 'fs';

console.log('🔧 إصلاح جميع مشاكل ربط السور والصفحات...\n');

// قراءة البيانات الحالية
const surahData = JSON.parse(fs.readFileSync('./src/data/surah.json', 'utf8'));
const juzData = JSON.parse(fs.readFileSync('./src/data/juz.json', 'utf8'));

console.log('1️⃣ إصلاح صفحات السور في surah.json...');

// خريطة السور والصفحات الصحيحة (حسب المصحف الشريف الحقيقي)
const correctSurahPages = {
  1: 1,     // الفاتحة
  2: 2,     // البقرة
  3: 50,    // آل عمران
  4: 77,    // النساء
  5: 106,   // المائدة
  6: 128,   // الأنعام
  7: 151,   // الأعراف
  8: 177,   // الأنفال
  9: 187,   // التوبة
  10: 208,  // يونس
  11: 221,  // هود
  12: 235,  // يوسف
  13: 249,  // الرعد
  14: 255,  // إبراهيم
  15: 262,  // الحجر
  16: 267,  // النحل
  17: 282,  // الإسراء
  18: 293,  // الكهف
  19: 305,  // مريم
  20: 312,  // طه
  21: 322,  // الأنبياء
  22: 332,  // الحج
  23: 342,  // المؤمنون
  24: 350,  // النور
  25: 359,  // الفرقان
  26: 367,  // الشعراء
  27: 377,  // النمل
  28: 385,  // القصص
  29: 396,  // العنكبوت
  30: 404,  // الروم
  31: 411,  // لقمان
  32: 415,  // السجدة
  33: 418,  // الأحزاب
  34: 428,  // سبأ
  35: 434,  // فاطر
  36: 440,  // يس
  37: 446,  // الصافات
  38: 453,  // ص
  39: 458,  // الزمر
  40: 467,  // غافر
  41: 477,  // فصلت
  42: 483,  // الشورى
  43: 489,  // الزخرف
  44: 496,  // الدخان
  45: 499,  // الجاثية
  46: 502,  // الأحقاف
  47: 507,  // محمد
  48: 511,  // الفتح
  49: 515,  // الحجرات
  50: 518,  // ق
  51: 520,  // الذاريات
  52: 523,  // الطور
  53: 526,  // النجم
  54: 528,  // القمر
  55: 531,  // الرحمن
  56: 534,  // الواقعة
  57: 537,  // الحديد
  58: 542,  // المجادلة
  59: 545,  // الحشر
  60: 549,  // الممتحنة
  61: 551,  // الصف
  62: 553,  // الجمعة
  63: 554,  // المنافقون
  64: 556,  // التغابن
  65: 558,  // الطلاق
  66: 560,  // التحريم
  67: 562,  // الملك
  68: 564,  // القلم
  69: 566,  // الحاقة
  70: 568,  // المعارج
  71: 570,  // نوح
  72: 572,  // الجن
  73: 574,  // المزمل
  74: 575,  // المدثر
  75: 577,  // القيامة
  76: 578,  // الإنسان
  77: 580,  // المرسلات
  78: 582,  // النبأ
  79: 583,  // النازعات
  80: 585,  // عبس
  81: 586,  // التكوير
  82: 587,  // الانفطار
  83: 587,  // المطففين
  84: 589,  // الانشقاق
  85: 590,  // البروج
  86: 591,  // الطارق
  87: 591,  // الأعلى
  88: 592,  // الغاشية
  89: 593,  // الفجر
  90: 594,  // البلد
  91: 595,  // الشمس
  92: 595,  // الليل
  93: 596,  // الضحى
  94: 596,  // الشرح
  95: 597,  // التين
  96: 597,  // العلق
  97: 598,  // القدر
  98: 598,  // البينة
  99: 599,  // الزلزلة
  100: 599, // العاديات
  101: 600, // القارعة
  102: 600, // التكاثر
  103: 601, // العصر
  104: 601, // الهمزة
  105: 602, // الفيل
  106: 602, // قريش
  107: 603, // الماعون
  108: 603, // الكوثر
  109: 603, // الكافرون
  110: 604, // النصر
  111: 604, // المسد
  112: 604, // الإخلاص
  113: 604, // الفلق
  114: 604  // الناس
};

// تحديث صفحات السور
const updatedSurahData = surahData.map(surah => {
  const surahId = parseInt(surah.index);
  const correctPage = correctSurahPages[surahId];
  
  if (correctPage) {
    return {
      ...surah,
      pages: correctPage.toString()
    };
  }
  
  return surah;
});

// حفظ البيانات المحدثة
fs.writeFileSync('./src/data/surah.json', JSON.stringify(updatedSurahData, null, 4), 'utf8');
console.log('✅ تم تحديث صفحات السور في surah.json');

console.log('\n2️⃣ إصلاح بيانات الأجزاء...');

// خريطة الأجزاء والصفحات الصحيحة
const correctJuzPages = {
  1: 1,    // الجزء الأول
  2: 22,   // الجزء الثاني
  3: 42,   // الجزء الثالث
  4: 62,   // الجزء الرابع
  5: 82,   // الجزء الخامس
  6: 102,  // الجزء السادس
  7: 122,  // الجزء السابع
  8: 142,  // الجزء الثامن
  9: 162,  // الجزء التاسع
  10: 182, // الجزء العاشر
  11: 202, // الجزء الحادي عشر
  12: 222, // الجزء الثاني عشر
  13: 242, // الجزء الثالث عشر
  14: 262, // الجزء الرابع عشر
  15: 282, // الجزء الخامس عشر
  16: 302, // الجزء السادس عشر
  17: 322, // الجزء السابع عشر
  18: 342, // الجزء الثامن عشر
  19: 362, // الجزء التاسع عشر
  20: 382, // الجزء العشرون
  21: 402, // الجزء الحادي والعشرون
  22: 422, // الجزء الثاني والعشرون
  23: 442, // الجزء الثالث والعشرون
  24: 462, // الجزء الرابع والعشرون
  25: 482, // الجزء الخامس والعشرون
  26: 502, // الجزء السادس والعشرون
  27: 522, // الجزء السابع والعشرون
  28: 542, // الجزء الثامن والعشرون
  29: 562, // الجزء التاسع والعشرون
  30: 582  // الجزء الثلاثون
};

// تحديث بيانات الأجزاء
const updatedJuzData = juzData.map((juz, index) => {
  const juzNumber = index + 1;
  const correctStartPage = correctJuzPages[juzNumber];
  
  return {
    ...juz,
    startPage: correctStartPage
  };
});

fs.writeFileSync('./src/data/juz.json', JSON.stringify(updatedJuzData, null, 2), 'utf8');
console.log('✅ تم تحديث بيانات الأجزاء');

console.log('\n3️⃣ التحقق من النتائج...');

// التحقق من سورة الناس
const nasData = updatedSurahData.find(s => parseInt(s.index) === 114);
console.log(`سورة الناس: صفحة ${nasData.pages} (يجب أن تكون 604)`);

// التحقق من الجزء الرابع
const juz4 = updatedJuzData[3]; // الفهرس 3 = الجزء 4
console.log(`الجزء الرابع: يبدأ من صفحة ${juz4.startPage} (يجب أن تكون 62)`);

console.log('\n✅ تم إصلاح جميع مشاكل الربط!');
