import React from 'react';

interface SurahHeaderWithImageProps {
  surahNameArabic: string;
  surahNameEnglish: string;
  surahNumber?: number;
  imageUrl: string; // رابط الصورة الأصلية
}

export const SurahHeaderWithImage: React.FC<SurahHeaderWithImageProps> = ({
  surahNameArabic,
  surahNameEnglish,
  surahNumber,
  imageUrl
}) => {
  return (
    <div className="relative w-full max-w-md mx-auto my-8">
      {/* الصورة الأصلية كخلفية */}
      <div 
        className="relative w-full h-48 bg-cover bg-center bg-no-repeat rounded-lg shadow-2xl"
        style={{ 
          backgroundImage: `url(${imageUrl})`,
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat'
        }}
      >
        {/* المحتوى النصي فوق الصورة */}
        <div className="absolute inset-0 flex flex-col items-center justify-center text-center p-6">
          <h2 className="text-3xl font-bold text-white mb-2 drop-shadow-2xl">
            {surahNameArabic}
          </h2>
          <p className="text-lg text-white/90 drop-shadow-lg">
            {surahNameEnglish}
            {surahNumber && ` • ${surahNumber}`}
          </p>
        </div>
      </div>
    </div>
  );
};

interface QuranPageWithImageBorderProps {
  children: React.ReactNode;
  borderImageUrl: string; // رابط صورة الإطار
  className?: string;
}

export const QuranPageWithImageBorder: React.FC<QuranPageWithImageBorderProps> = ({
  children,
  borderImageUrl,
  className = ''
}) => {
  return (
    <div className={`relative max-w-4xl mx-auto my-8 ${className}`}>
      {/* صورة الإطار الأصلية */}
      <div
        className="absolute inset-0 w-full h-full bg-cover bg-center bg-no-repeat opacity-30"
        style={{
          backgroundImage: `url(${borderImageUrl})`,
          backgroundSize: '100% 100%',
          filter: 'sepia(20%) saturate(150%) hue-rotate(15deg)'
        }}
      />

      {/* المحتوى */}
      <div className="relative z-10 p-16 min-h-[600px]">
        {children}
      </div>
    </div>
  );
};

// مكون لعرض الصورة كما هي مع إمكانية إضافة نص
interface DirectImageDisplayProps {
  imageUrl: string;
  alt: string;
  width?: string;
  height?: string;
  className?: string;
  overlay?: React.ReactNode; // محتوى يظهر فوق الصورة
}

export const DirectImageDisplay: React.FC<DirectImageDisplayProps> = ({
  imageUrl,
  alt,
  width = "100%",
  height = "auto",
  className = "",
  overlay
}) => {
  return (
    <div className={`relative inline-block ${className}`}>
      <img 
        src={imageUrl} 
        alt={alt}
        style={{ width, height }}
        className="object-contain"
      />
      {overlay && (
        <div className="absolute inset-0 flex items-center justify-center">
          {overlay}
        </div>
      )}
    </div>
  );
};

// مكون مخصص لاستخدام الصور في تخطيط الصفحة
interface IslamicPageLayoutProps {
  headerImage: string;
  borderImage: string;
  surahName: string;
  surahNameEnglish: string;
  surahNumber: number;
  children: React.ReactNode;
}

export const IslamicPageLayout: React.FC<IslamicPageLayoutProps> = ({
  headerImage,
  borderImage,
  surahName,
  surahNameEnglish,
  surahNumber,
  children
}) => {
  return (
    <div className="w-full">
      {/* رأس السورة بالصورة الأصلية */}
      <SurahHeaderWithImage
        surahNameArabic={surahName}
        surahNameEnglish={surahNameEnglish}
        surahNumber={surahNumber}
        imageUrl={headerImage}
      />
      
      {/* محتوى الصفحة مع إطار الصورة */}
      <QuranPageWithImageBorder borderImageUrl={borderImage}>
        {children}
      </QuranPageWithImageBorder>
    </div>
  );
};

export default {
  SurahHeaderWithImage,
  QuranPageWithImageBorder,
  DirectImageDisplay,
  IslamicPageLayout
};
