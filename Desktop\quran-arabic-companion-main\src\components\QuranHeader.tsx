import { Search, Settings, ChevronRight } from "lucide-react";
import { SimpleSurahHeader } from "./SimpleImageOrnaments";

interface QuranHeaderProps {
  title: string;
  surahNameArabic?: string;
  surahNameEnglish?: string;
  surahNumber?: number;
  onSearch?: () => void;
  onSettings?: () => void;
  onNext?: () => void;
  useOrnament?: boolean;
}

const QuranHeader = ({
  title,
  surahNameArabic,
  surahNameEnglish,
  surahNumber,
  onSearch,
  onSettings,
  onNext,
  useOrnament = false
}: QuranHeaderProps) => {
  // If ornament is requested and we have surah data, use the ornamental header
  if (useOrnament && surahNameArabic && surahNameEnglish) {
    return (
      <div className="bg-gradient-to-b from-slate-50 to-white">
        <div className="bg-gray-900 text-white p-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            {onSettings && (
              <button onClick={onSettings} className="text-gray-300 hover:text-white transition-colors">
                <Settings size={20} />
              </button>
            )}
            {onSearch && (
              <button onClick={onSearch} className="text-gray-300 hover:text-white transition-colors">
                <Search size={20} />
              </button>
            )}
          </div>

          <h1 className="text-lg font-bold text-center">{title}</h1>

          <div className="flex items-center gap-4">
            {onNext && (
              <button onClick={onNext} className="text-gray-300 hover:text-white transition-colors">
                <ChevronRight size={20} />
              </button>
            )}
          </div>
        </div>

        {/* Islamic Ornamental Surah Header */}
        <SimpleSurahHeader
          surahNameArabic={surahNameArabic}
          surahNameEnglish={surahNameEnglish}
          surahNumber={surahNumber}
        />
      </div>
    );
  }

  // Default header without ornament
  return (
    <div className="bg-gray-900 text-white p-4 flex items-center justify-between">
      <div className="flex items-center gap-4">
        {onSettings && (
          <button onClick={onSettings} className="text-gray-300 hover:text-white transition-colors">
            <Settings size={20} />
          </button>
        )}
        {onSearch && (
          <button onClick={onSearch} className="text-gray-300 hover:text-white transition-colors">
            <Search size={20} />
          </button>
        )}
      </div>

      <h1 className="text-lg font-bold text-center">{title}</h1>

      <div className="flex items-center gap-4">
        {onNext && (
          <button onClick={onNext} className="text-gray-300 hover:text-white transition-colors">
            <ChevronRight size={20} />
          </button>
        )}
      </div>
    </div>
  );
};

export default QuranHeader;
