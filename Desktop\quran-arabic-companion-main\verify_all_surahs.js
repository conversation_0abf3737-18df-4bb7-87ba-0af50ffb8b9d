import fs from 'fs';

console.log('🔍 التحقق الشامل من صحة جميع السور...\n');

// قراءة بيانات السور من surah.json
const surahData = JSON.parse(fs.readFileSync('./src/data/surah.json', 'utf8'));

// عدد الآيات الصحيح لكل سورة (حسب المصحف الشريف)
const correctVerseCounts = {
  1: 7, 2: 286, 3: 200, 4: 176, 5: 120, 6: 165, 7: 206, 8: 75, 9: 129, 10: 109,
  11: 123, 12: 111, 13: 43, 14: 52, 15: 99, 16: 128, 17: 111, 18: 110, 19: 98, 20: 135,
  21: 112, 22: 78, 23: 118, 24: 64, 25: 77, 26: 227, 27: 93, 28: 88, 29: 69, 30: 60,
  31: 34, 32: 30, 33: 73, 34: 54, 35: 45, 36: 83, 37: 182, 38: 88, 39: 75, 40: 85,
  41: 54, 42: 53, 43: 89, 44: 59, 45: 37, 46: 35, 47: 38, 48: 29, 49: 18, 50: 45,
  51: 60, 52: 49, 53: 62, 54: 55, 55: 78, 56: 96, 57: 29, 58: 22, 59: 24, 60: 13,
  61: 14, 62: 11, 63: 11, 64: 18, 65: 12, 66: 12, 67: 30, 68: 52, 69: 52, 70: 44,
  71: 28, 72: 28, 73: 20, 74: 56, 75: 40, 76: 31, 77: 50, 78: 40, 79: 46, 80: 42,
  81: 29, 82: 19, 83: 36, 84: 25, 85: 22, 86: 17, 87: 19, 88: 26, 89: 30, 90: 20,
  91: 15, 92: 21, 93: 11, 94: 8, 95: 8, 96: 19, 97: 5, 98: 8, 99: 8, 100: 11,
  101: 11, 102: 8, 103: 3, 104: 9, 105: 5, 106: 4, 107: 7, 108: 3, 109: 6, 110: 3,
  111: 5, 112: 4, 113: 5, 114: 6
};

let allCorrect = true;
let errors = [];

console.log('📋 فحص جميع السور (1-114):');

for (let surahId = 1; surahId <= 114; surahId++) {
  try {
    // قراءة ملف السورة
    const surahFile = `./src/data/surah/surah_${surahId}.json`;
    
    if (!fs.existsSync(surahFile)) {
      errors.push(`❌ السورة ${surahId}: ملف غير موجود`);
      allCorrect = false;
      continue;
    }
    
    const surahContent = JSON.parse(fs.readFileSync(surahFile, 'utf8'));
    
    // التحقق من الفهرس
    const actualIndex = parseInt(surahContent.index);
    if (actualIndex !== surahId) {
      errors.push(`❌ السورة ${surahId}: فهرس خاطئ (${actualIndex})`);
      allCorrect = false;
    }
    
    // التحقق من عدد الآيات
    const expectedCount = correctVerseCounts[surahId];
    const actualCount = surahContent.count;
    
    if (actualCount !== expectedCount) {
      errors.push(`❌ السورة ${surahId}: عدد آيات خاطئ (${actualCount} بدلاً من ${expectedCount})`);
      allCorrect = false;
    }
    
    // التحقق من وجود جميع الآيات
    const verses = surahContent.verse;
    let missingVerses = [];
    
    for (let v = 1; v <= expectedCount; v++) {
      if (!verses[`verse_${v}`] || verses[`verse_${v}`].trim() === '') {
        missingVerses.push(v);
      }
    }
    
    if (missingVerses.length > 0) {
      errors.push(`❌ السورة ${surahId}: آيات مفقودة أو فارغة (${missingVerses.join(', ')})`);
      allCorrect = false;
    }
    
    // التحقق من عدم وجود آيات زائدة
    const actualVerseKeys = Object.keys(verses).filter(key => key.startsWith('verse_') && key !== 'verse_0');
    if (actualVerseKeys.length !== expectedCount) {
      errors.push(`❌ السورة ${surahId}: عدد مفاتيح الآيات خاطئ (${actualVerseKeys.length})`);
      allCorrect = false;
    }
    
    // طباعة تقدم كل 10 سور
    if (surahId % 10 === 0) {
      console.log(`✅ تم فحص السور 1-${surahId}`);
    }
    
  } catch (error) {
    errors.push(`❌ السورة ${surahId}: خطأ في القراءة - ${error.message}`);
    allCorrect = false;
  }
}

console.log('\n📊 نتائج الفحص:');

if (allCorrect) {
  console.log('🎉 ممتاز! جميع السور صحيحة ومكتملة (1-114)');
} else {
  console.log(`❌ توجد ${errors.length} مشكلة في السور:`);
  errors.forEach(error => console.log(`   ${error}`));
}

// فحص خاص للسور المذكورة
console.log('\n🔍 فحص خاص للسور المذكورة:');

// سورة النبأ (78)
try {
  const naba = JSON.parse(fs.readFileSync('./src/data/surah/surah_78.json', 'utf8'));
  console.log(`سورة النبأ (78): ${naba.count} آية (يجب أن تكون 40)`);
  if (naba.count === 40) {
    console.log('✅ سورة النبأ: عدد الآيات صحيح');
  } else {
    console.log('❌ سورة النبأ: عدد الآيات خاطئ');
  }
} catch (error) {
  console.log(`❌ سورة النبأ: خطأ في القراءة - ${error.message}`);
}

// سورة النازعات (79)
try {
  const naziat = JSON.parse(fs.readFileSync('./src/data/surah/surah_79.json', 'utf8'));
  console.log(`سورة النازعات (79): ${naziat.count} آية (يجب أن تكون 46)`);
  if (naziat.count === 46) {
    console.log('✅ سورة النازعات: عدد الآيات صحيح');
  } else {
    console.log('❌ سورة النازعات: عدد الآيات خاطئ');
  }
} catch (error) {
  console.log(`❌ سورة النازعات: خطأ في القراءة - ${error.message}`);
}

console.log('\n🔧 التوصيات:');
if (allCorrect) {
  console.log('✅ جميع السور صحيحة - يمكن المتابعة');
  console.log('🔄 تحقق من ملف pages.json وطريقة عرض المحتوى في التطبيق');
} else {
  console.log('❌ يجب إصلاح السور المذكورة أعلاه قبل المتابعة');
  console.log('📚 استخدم مصدر موثوق مثل موقع القرآن الكريم الرسمي');
}
