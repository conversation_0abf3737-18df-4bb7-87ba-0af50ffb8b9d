import fs from 'fs';
import path from 'path';

console.log('🔧 بدء إصلاح بيانات المصحف...\n');

// قراءة البيانات الحالية
const surahData = JSON.parse(fs.readFileSync('./src/data/surah.json', 'utf8'));
const juzData = JSON.parse(fs.readFileSync('./src/data/juz.json', 'utf8'));
let pagesData = [];

try {
  pagesData = JSON.parse(fs.readFileSync('./src/data/pages.json', 'utf8'));
} catch (error) {
  console.log('⚠️ ملف pages.json غير مكتمل، سيتم إنشاؤه من جديد');
}

console.log('📊 تحليل البيانات الحالية:');
console.log(`- عدد السور: ${surahData.length}`);
console.log(`- عدد الأجزاء: ${juzData.length}`);
console.log(`- عدد الصفحات الحالية: ${pagesData.length}`);

// 1. إصلاح مشكلة ربط السور بالصفحات
console.log('\n🔍 فحص ربط السور بالصفحات...');

// التحقق من وجود جميع السور في ملفات surah/
const missingSurahs = [];
for (let i = 1; i <= 114; i++) {
  const surahFile = `./src/data/surah/surah_${i}.json`;
  if (!fs.existsSync(surahFile)) {
    missingSurahs.push(i);
  }
}

if (missingSurahs.length > 0) {
  console.log(`❌ السور المفقودة: ${missingSurahs.join(', ')}`);
} else {
  console.log('✅ جميع ملفات السور موجودة (1-114)');
}

// 2. إصلاح مشكلة الأجزاء - تحديد الصفحة الصحيحة لكل جزء
console.log('\n🔧 إصلاح بيانات الأجزاء...');

// خريطة الأجزاء والصفحات الصحيحة (حسب المصحف الشريف)
const correctJuzPages = {
  1: 1,    // الجزء الأول يبدأ من الصفحة 1
  2: 22,   // الجزء الثاني يبدأ من الصفحة 22
  3: 42,   // الجزء الثالث يبدأ من الصفحة 42
  4: 62,   // الجزء الرابع يبدأ من الصفحة 62
  5: 82,   // الجزء الخامس يبدأ من الصفحة 82
  6: 102,  // الجزء السادس يبدأ من الصفحة 102
  7: 122,  // الجزء السابع يبدأ من الصفحة 122
  8: 142,  // الجزء الثامن يبدأ من الصفحة 142
  9: 162,  // الجزء التاسع يبدأ من الصفحة 162
  10: 182, // الجزء العاشر يبدأ من الصفحة 182
  11: 202, // الجزء الحادي عشر يبدأ من الصفحة 202
  12: 222, // الجزء الثاني عشر يبدأ من الصفحة 222
  13: 242, // الجزء الثالث عشر يبدأ من الصفحة 242
  14: 262, // الجزء الرابع عشر يبدأ من الصفحة 262
  15: 282, // الجزء الخامس عشر يبدأ من الصفحة 282
  16: 302, // الجزء السادس عشر يبدأ من الصفحة 302
  17: 322, // الجزء السابع عشر يبدأ من الصفحة 322
  18: 342, // الجزء الثامن عشر يبدأ من الصفحة 342
  19: 362, // الجزء التاسع عشر يبدأ من الصفحة 362
  20: 382, // الجزء العشرون يبدأ من الصفحة 382
  21: 402, // الجزء الحادي والعشرون يبدأ من الصفحة 402
  22: 422, // الجزء الثاني والعشرون يبدأ من الصفحة 422
  23: 442, // الجزء الثالث والعشرون يبدأ من الصفحة 442
  24: 462, // الجزء الرابع والعشرون يبدأ من الصفحة 462
  25: 482, // الجزء الخامس والعشرون يبدأ من الصفحة 482
  26: 502, // الجزء السادس والعشرون يبدأ من الصفحة 502
  27: 522, // الجزء السابع والعشرون يبدأ من الصفحة 522
  28: 542, // الجزء الثامن والعشرون يبدأ من الصفحة 542
  29: 562, // الجزء التاسع والعشرون يبدأ من الصفحة 562
  30: 582  // الجزء الثلاثون يبدأ من الصفحة 582
};

// تحديث بيانات الأجزاء
const updatedJuzData = juzData.map((juz, index) => {
  const juzNumber = index + 1;
  const correctStartPage = correctJuzPages[juzNumber];
  
  return {
    ...juz,
    startPage: correctStartPage
  };
});

// حفظ بيانات الأجزاء المحدثة
fs.writeFileSync('./src/data/juz.json', JSON.stringify(updatedJuzData, null, 2), 'utf8');
console.log('✅ تم تحديث بيانات الأجزاء بالصفحات الصحيحة');

// 3. التحقق من صحة ربط السور
console.log('\n🔍 فحص ربط السور بالمحتوى...');

let surahMappingErrors = [];

for (let i = 1; i <= 114; i++) {
  try {
    // قراءة ملف السورة
    const surahFile = `./src/data/surah/surah_${i}.json`;
    const surahContent = JSON.parse(fs.readFileSync(surahFile, 'utf8'));
    
    // التحقق من صحة الفهرس
    if (parseInt(surahContent.index) !== i) {
      surahMappingErrors.push({
        surahNumber: i,
        expectedIndex: i,
        actualIndex: surahContent.index,
        error: 'فهرس السورة غير صحيح'
      });
    }
    
    // التحقق من وجود الآيات
    if (!surahContent.verse || Object.keys(surahContent.verse).length === 0) {
      surahMappingErrors.push({
        surahNumber: i,
        error: 'لا توجد آيات في ملف السورة'
      });
    }
    
  } catch (error) {
    surahMappingErrors.push({
      surahNumber: i,
      error: `خطأ في قراءة ملف السورة: ${error.message}`
    });
  }
}

if (surahMappingErrors.length > 0) {
  console.log('❌ أخطاء في ربط السور:');
  surahMappingErrors.forEach(error => {
    console.log(`   السورة ${error.surahNumber}: ${error.error}`);
  });
} else {
  console.log('✅ جميع السور مربوطة بشكل صحيح');
}

console.log('\n✨ انتهى إصلاح البيانات!');
console.log('\n📋 ملخص الإصلاحات:');
console.log('1. ✅ تم تحديث صفحات بداية الأجزاء');
console.log('2. ✅ تم فحص ربط السور بالمحتوى');
console.log('\n🔄 يرجى إعادة تشغيل التطبيق لرؤية التحسينات');
