import { useState, useEffect } from "react";
import { ChevronRight, ChevronLeft, Share, BookmarkIcon, Settings, Info } from "lucide-react";
import QuranHeader from "./QuranHeader";
import { suras, ajzaa, quranData } from "../data/quranData";

interface QuranReaderProps {
  initialSuraId: number;
  onBack: () => void;
  onShowSettings: () => void;
  onAyaRead: (suraId: number, ayaId: number) => void;
  quranSettings: {
    selectedMushaf: 'connected' | 'separate',
    selectedColor: string,
    selectedMode: 'dark' | 'light' | 'auto',
    fontSize: number
  };
}

const QuranReader = ({ initialSuraId, onBack, onShowSettings, onAyaRead, quranSettings }: QuranReaderProps) => {
  const [currentSuraId, setCurrentSuraId] = useState(initialSuraId);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [savedSuraId, setSavedSuraId] = useState<number | null>(null);
  const [showBookmarkDialog, setShowBookmarkDialog] = useState(false);
  const [showShareMenu, setShowShareMenu] = useState(false);
  const [showQuranActions, setShowQuranActions] = useState(false);
  const [selectedBookmarkColor, setSelectedBookmarkColor] = useState('orange');
  const [bookmark, setBookmark] = useState<{ suraId: number; ayaId: number; color: string } | null>(null);

  useEffect(() => {
    setCurrentSuraId(initialSuraId);
    const currentSura = quranData.find((sura: any) => sura.id === initialSuraId);
    if (currentSura) {
      setCurrentPage(parseInt(currentSura.page));
      const firstAyaOfSura = currentSura.verses[0];
      if (firstAyaOfSura) {
        onAyaRead(initialSuraId, firstAyaOfSura.id);
      }
    }
  }, [initialSuraId]);

  useEffect(() => {
    // عند فتح المصحف، جلب الفاصلة من localStorage
    const savedBookmark = localStorage.getItem('bookmark');
    if (savedBookmark) {
      setBookmark(JSON.parse(savedBookmark));
    }
  }, []);

  const currentSura = quranData.find((sura: any) => sura.id === currentSuraId);
  // const currentJuz = ajzaa.find(juz => juz.id === pageContent?.juz_id);

  const handleBookmark = () => {
    setSavedSuraId(currentSuraId);
    setShowBookmarkDialog(true);
    // احفظ الفاصلة في localStorage
    const firstAya = currentSura?.verses[0]?.id || 1;
    const bookmarkObj = { suraId: currentSuraId, ayaId: firstAya, color: selectedBookmarkColor };
    localStorage.setItem('bookmark', JSON.stringify(bookmarkObj));
    setBookmark(bookmarkObj);
    setTimeout(() => setShowBookmarkDialog(false), 2000);
  };

  const handleShare = () => {
    setShowShareMenu(!showShareMenu);
  };

  const handleQuranUsage = () => {
    setShowQuranActions(!showQuranActions);
  };

  const handleSuraChange = (newSuraId: number) => {
    if (newSuraId >= 1 && newSuraId <= quranData.length) {
      setCurrentSuraId(newSuraId);
      const firstAyaOfSura = quranData.find((sura: any) => sura.id === newSuraId)?.verses[0];
      if (firstAyaOfSura) {
        onAyaRead(newSuraId, firstAyaOfSura.id);
      }
    }
  };

  const bookmarkColors = [
    { name: 'orange', color: 'bg-orange-500' },
    { name: 'blue', color: 'bg-blue-500' },
    { name: 'red', color: 'bg-red-500' },
    { name: 'green', color: 'bg-green-500' },
  ];

  const fontSize = quranSettings.fontSize;
  const verseNumberColor = quranSettings.selectedColor === 'gray' ? 'bg-gray-300' :
    quranSettings.selectedColor === 'red' ? 'bg-red-500' :
    quranSettings.selectedColor === 'blue' ? 'bg-blue-500' :
    quranSettings.selectedColor === 'green' ? 'bg-green-500' :
    quranSettings.selectedColor === 'dark' ? 'bg-gray-600' : 'bg-gray-300';

  return (
    <div className="min-h-screen bg-gray-900 text-white relative">
      <QuranHeader 
        title="المصحف"
        onBack={onBack}
        onSettings={onShowSettings}
      />

      {/* Sura info */}
      <div className="bg-gray-800 px-4 py-2 text-center flex items-center justify-between">
        <span className="text-gray-400 text-sm">السورة {currentSura?.id}</span>
        <span className="text-gray-400 text-sm">صفحة {currentPage}</span>
        <span className="text-white font-bold text-sm">{currentSura?.name}</span>
      </div>

      {/* Bookmark colors */}
      <div className="bg-gray-800 px-4 py-2 flex justify-end items-center gap-2">
        <span className="text-gray-300 text-sm ml-4">أضف فاصلة</span>
        {bookmarkColors.map((color) => (
          <div
            key={color.name}
            onClick={() => setSelectedBookmarkColor(color.name)}
            className={`w-6 h-8 ${color.color} rounded-sm cursor-pointer ${
              selectedBookmarkColor === color.name ? 'border-2 border-green-500' : ''
            }`}
          ></div>
        ))}
      </div>

      {/* Progress bar (conceptual for sura progress) */}
      <div className="bg-gray-800 px-4 py-1">
        <div className="w-full bg-gray-700 rounded-full h-1">
          <div className="bg-green-400 h-1 rounded-full" style={{ width: `${(currentSuraId / quranData.length) * 100}%` }}></div>
        </div>
      </div>

      {/* Enhanced Quran text with traditional design */}
      <div className="p-6 max-w-4xl mx-auto arabic-text mushaf-page-enhanced rounded-2xl shadow-2xl"
           style={{ fontSize: `${fontSize}px`, lineHeight: '2.5' }}>

        {/* اسم السورة مع التصميم الجديد */}
        <div className="text-center mb-8">
          <div className="relative inline-block surah-header">
            <div className="surah-header-corner top-left"></div>
            <div className="surah-header-corner top-right"></div>
            <div className="surah-header-corner bottom-left"></div>
            <div className="surah-header-corner bottom-right"></div>

            <div className="text-white text-2xl font-bold relative z-10"
                 style={{
                   fontFamily: 'Amiri, serif',
                   textShadow: '2px 2px 4px rgba(0,0,0,0.5)'
                 }}>
              سُورَةُ {currentSura?.name}
            </div>
          </div>
        </div>

        {/* البسملة الجديدة */}
        {currentSura?.id !== 1 && currentSura?.id !== 9 && (
          <div className="text-center mb-8">
            <div className="bismillah-container">
              <div className="text-white text-xl font-semibold relative z-10"
                   style={{
                     fontFamily: 'Amiri, serif',
                     textShadow: '1px 1px 3px rgba(0,0,0,0.5)',
                     letterSpacing: '0.05em'
                   }}>
                بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
              </div>
            </div>
          </div>
        )}

        {quranSettings.selectedMushaf === 'connected' ? (
          <div className="space-y-6 leading-loose text-right islamic-pattern rounded-xl p-6">
            <div className="text-white text-right mb-4 quran-text" dir="rtl">
              {currentSura?.verses.map((verse: any, idx: number) => (
                <span key={verse.id} className="verse-text-enhanced">
                  {verse.text}
                  <span className="verse-number-traditional">
                    {verse.id}
                  </span>
                  {idx !== currentSura.verses.length - 1 && ' '}
                </span>
              ))}
            </div>
          </div>
        ) : (
          <div className="space-y-6 leading-loose text-right">
            {currentSura?.verses.map((verse: any) => (
              <div key={verse.id} className="text-white text-right mb-6 bg-gray-800 rounded-xl p-4 verse-card" dir="rtl">
                <span className="verse-text-enhanced quran-text">
                  {verse.text}
                </span>
                <span className="verse-number-traditional">
                  {verse.id}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Navigation buttons */}
      <div className="fixed bottom-4 left-0 right-0 flex justify-center gap-4">
        <button
          onClick={() => handleSuraChange(currentSuraId - 1)}
          disabled={currentSuraId <= 1}
          className="bg-gray-800 p-3 rounded-full disabled:opacity-50"
        >
          <ChevronRight size={24} />
        </button>
        <button
          onClick={() => handleSuraChange(currentSuraId + 1)}
          disabled={currentSuraId >= quranData.length}
          className="bg-gray-800 p-3 rounded-full disabled:opacity-50"
        >
          <ChevronLeft size={24} />
        </button>
      </div>

      {/* Bookmark dialog */}
      {showBookmarkDialog && (
        <div className="fixed bottom-20 left-0 right-0 bg-gray-800 p-4 mx-4 rounded-lg">
          <div className="text-center">
            <h3 className="text-white font-bold mb-2">{currentSura?.name}</h3>
            <p className="text-green-400 text-sm">اضف فاصلة للسورة {currentSura?.name}</p>
            <div className="flex justify-center gap-2 mt-3">
              {bookmarkColors.map((color) => (
                <div
                  key={color.name}
                  onClick={() => setSelectedBookmarkColor(color.name)}
                  className={`w-6 h-8 ${color.color} rounded-sm cursor-pointer ${
                    selectedBookmarkColor === color.name ? 'border-2 border-green-500' : ''
                  }`}
                ></div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Share menu */}
      {showShareMenu && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-gray-800 rounded-lg p-6 mx-4 w-full max-w-sm">
            <button 
              onClick={() => setShowShareMenu(false)}
              className="absolute top-4 right-4 text-gray-400"
            >
              ×
            </button>
            <div className="text-center">
              <Share className="w-8 h-8 text-blue-400 mx-auto mb-4" />
              <p className="text-white mb-4">مشاركة الآية</p>
              <div className="space-y-3">
                <button className="w-full bg-blue-600 text-white py-2 rounded-lg">
                  مشاركة كصورة
                </button>
                <button className="w-full bg-green-600 text-white py-2 rounded-lg">
                  مشاركة كنص
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quran usage guide */}
      {showQuranActions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end">
          <div className="bg-gray-800 rounded-t-lg p-6 w-full">
            <div className="w-12 h-1 bg-gray-600 rounded-full mx-auto mb-6"></div>
            <h3 className="text-white font-bold text-center mb-6">استخدام المصحف</h3>
            <div className="space-y-4">
              <button className="w-full text-right text-white py-3 border-b border-gray-700">
                لمشاركة الآيات أو إضافة فاصلة عند الآية، اضغط مرتين على الآية.
              </button>
              <button className="w-full text-right text-white py-3 border-b border-gray-700">
                اضغط مرة واحدة لحفظ السورة كآخر سورة وصلت إليها.
              </button>
              <button className="w-full text-right text-white py-3 border-b border-gray-700">
                لتصفح السور حرك الشاشة للأعلى أو الأسفل.
              </button>
              <button className="w-full text-right text-white py-3">
                يمكنك وضع فاصلة للسورة بالضغط على اسم السورة.
              </button>
            </div>
            <button 
              onClick={() => setShowQuranActions(false)}
              className="w-full bg-gray-700 text-white py-3 rounded-lg mt-6"
            >
              إغلاق
            </button>
          </div>
        </div>
      )}

      {/* زر الرجوع إلى الفاصلة */}
      {bookmark && (
        <div className="bg-yellow-400 text-black text-center py-2 cursor-pointer" onClick={() => {
          setCurrentSuraId(bookmark.suraId);
          // يمكن إضافة منطق للتمرير إلى الآية لاحقاً
        }}>
          الرجوع إلى الفاصلة
        </div>
      )}
    </div>
  );
};

export default QuranReader;
