import React from 'react';
import '../styles/islamic-ornaments.css';

interface SurahHeaderOrnamentProps {
  surahNameArabic: string;
  surahNameEnglish: string;
  surahNumber?: number;
}

export const SurahHeaderOrnament: React.FC<SurahHeaderOrnamentProps> = ({
  surahNameArabic,
  surahNameEnglish,
  surahNumber
}) => {
  return (
    <div className="surah-header-ornament">
      <svg
        className="surah-header-frame"
        viewBox="0 0 400 200"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style={{stopColor: '#FFD700', stopOpacity: 1}} />
            <stop offset="50%" style={{stopColor: '#FFA500', stopOpacity: 1}} />
            <stop offset="100%" style={{stopColor: '#FF8C00', stopOpacity: 1}} />
          </linearGradient>
          <filter id="glow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        <path d="M50 50
                 Q50 30 70 30
                 L130 30
                 Q150 10 200 10
                 Q250 10 270 30
                 L330 30
                 Q350 30 350 50
                 L350 80
                 Q370 100 370 120
                 Q370 140 350 160
                 L350 170
                 Q350 190 330 190
                 L270 190
                 Q250 210 200 210
                 Q150 210 130 190
                 L70 190
                 Q50 190 50 170
                 L50 160
                 Q30 140 30 120
                 Q30 100 50 80
                 Z"
              fill="none"
              stroke="url(#goldGradient)"
              strokeWidth="3"
              filter="url(#glow)"/>

        <path d="M70 60
                 Q70 45 85 45
                 L140 45
                 Q160 30 200 30
                 Q240 30 260 45
                 L315 45
                 Q330 45 330 60
                 L330 85
                 Q345 100 345 120
                 Q345 140 330 155
                 L330 160
                 Q330 175 315 175
                 L260 175
                 Q240 190 200 190
                 Q160 190 140 175
                 L85 175
                 Q70 175 70 160
                 L70 155
                 Q55 140 55 120
                 Q55 100 70 85
                 Z"
              fill="rgba(255, 215, 0, 0.1)"
              stroke="url(#goldGradient)"
              strokeWidth="1.5"/>

        <circle cx="80" cy="70" r="3" fill="url(#goldGradient)"/>
        <circle cx="320" cy="70" r="3" fill="url(#goldGradient)"/>
        <circle cx="80" cy="150" r="3" fill="url(#goldGradient)"/>
        <circle cx="320" cy="150" r="3" fill="url(#goldGradient)"/>

        <path d="M190 110 Q200 100 210 110 Q200 120 190 110 Z" fill="url(#goldGradient)" opacity="0.7"/>
      </svg>

      <div className="surah-header-content">
        <div className="surah-name-arabic">{surahNameArabic}</div>
        <div className="surah-name-english">
          {surahNameEnglish}
          {surahNumber && ` • ${surahNumber}`}
        </div>
      </div>
    </div>
  );
};

interface BismillahOrnamentProps {
  className?: string;
}

export const BismillahOrnament: React.FC<BismillahOrnamentProps> = ({ className = '' }) => {
  return (
    <div className={`bismillah-ornament ${className}`}>
      <div className="bismillah-text">
        بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
      </div>
    </div>
  );
};

interface VerseNumberOrnamentProps {
  number: number;
  className?: string;
}

export const VerseNumberOrnament: React.FC<VerseNumberOrnamentProps> = ({ 
  number, 
  className = '' 
}) => {
  return (
    <span className={`verse-number-ornament ${className}`}>
      {number}
    </span>
  );
};

interface QuranPageOrnamentProps {
  children: React.ReactNode;
  className?: string;
}

export const QuranPageOrnament: React.FC<QuranPageOrnamentProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className={`quran-page-ornament ${className}`}>
      <div className="quran-page-content">
        {children}
      </div>
    </div>
  );
};

interface SectionDividerProps {
  className?: string;
}

export const SectionDivider: React.FC<SectionDividerProps> = ({ className = '' }) => {
  return (
    <div className={`section-divider ${className}`}>
      <span className="islamic-star"></span>
    </div>
  );
};

// Export all components as default
export default {
  SurahHeaderOrnament,
  BismillahOrnament,
  VerseNumberOrnament,
  QuranPageOrnament,
  SectionDivider
};
