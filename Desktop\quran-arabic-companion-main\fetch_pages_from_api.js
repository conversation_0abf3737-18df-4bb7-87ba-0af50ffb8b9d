import fs from 'fs';

// دالة لتحميل صفحة من API باستخدام fetch
async function fetchPage(pageNumber) {
  try {
    const url = `http://api.alquran.cloud/v1/page/${pageNumber}/quran-uthmani`;
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.code === 200) {
      return data.data;
    } else {
      throw new Error(`API Error: ${data.status}`);
    }
  } catch (error) {
    throw error;
  }
}


// دالة لتحويل بيانات API إلى تنسيق التطبيق
function convertPageData(apiPageData) {
  const surahs = {};
  
  // تجميع الآيات حسب السورة
  apiPageData.ayahs.forEach(ayah => {
    const surahNumber = ayah.surah.number;
    const surahName = ayah.surah.name.replace(/سُورَةُ /, '').replace(/ٱل/, 'ال');
    
    if (!surahs[surahNumber]) {
      surahs[surahNumber] = {
        surah: surahNumber,
        name: surahName,
        verses: []
      };
    }
    
    surahs[surahNumber].verses.push(ayah.numberInSurah);
  });
  
  return {
    page: apiPageData.number,
    juz: apiPageData.ayahs[0].juz,
    surahs: Object.values(surahs)
  };
}

// دالة رئيسية لتحميل جميع الصفحات
async function fetchAllPages() {
  console.log('🔄 بدء تحميل الصفحات من API...');
  
  const pages = [];
  const batchSize = 10; // تحميل 10 صفحات في كل مرة لتجنب إرهاق الخادم
  
  for (let i = 1; i <= 604; i += batchSize) {
    const batch = [];
    const endIndex = Math.min(i + batchSize - 1, 604);
    
    console.log(`📥 تحميل الصفحات ${i} إلى ${endIndex}...`);
    
    // تحميل مجموعة من الصفحات بشكل متوازي
    for (let pageNum = i; pageNum <= endIndex; pageNum++) {
      batch.push(fetchPage(pageNum));
    }

    try {
      const batchResults = await Promise.all(batch);
      
      // تحويل البيانات وإضافتها للمصفوفة
      batchResults.forEach(apiPageData => {
        const convertedPage = convertPageData(apiPageData);
        pages.push(convertedPage);
      });
      
      console.log(`✅ تم تحميل ${batchResults.length} صفحة`);
      
      // انتظار قصير بين المجموعات لتجنب إرهاق الخادم
      if (endIndex < 604) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
    } catch (error) {
      console.error(`❌ خطأ في تحميل الصفحات ${i}-${endIndex}:`, error.message);
      
      // في حالة الخطأ، حاول تحميل الصفحات واحدة تلو الأخرى
      for (let pageNum = i; pageNum <= endIndex; pageNum++) {
        try {
          const apiPageData = await fetchPage(pageNum);
          const convertedPage = convertPageData(apiPageData);
          pages.push(convertedPage);
          console.log(`✅ تم تحميل الصفحة ${pageNum}`);
          
          // انتظار قصير بين الصفحات
          await new Promise(resolve => setTimeout(resolve, 200));
          
        } catch (pageError) {
          console.error(`❌ خطأ في تحميل الصفحة ${pageNum}:`, pageError.message);
        }
      }
    }
  }
  
  // ترتيب الصفحات حسب رقم الصفحة
  pages.sort((a, b) => a.page - b.page);
  
  // حفظ البيانات
  fs.writeFileSync('./src/data/pages.json', JSON.stringify(pages, null, 2), 'utf8');
  
  console.log(`🎉 تم تحميل وحفظ ${pages.length} صفحة بنجاح!`);
  
  // إحصائيات سريعة
  const totalSurahs = new Set();
  const totalJuzs = new Set();
  
  pages.forEach(page => {
    totalJuzs.add(page.juz);
    page.surahs.forEach(surah => {
      totalSurahs.add(surah.surah);
    });
  });
  
  console.log(`📊 الإحصائيات:`);
  console.log(`   - عدد الصفحات: ${pages.length}`);
  console.log(`   - عدد السور: ${totalSurahs.size}`);
  console.log(`   - عدد الأجزاء: ${totalJuzs.size}`);
}

// تشغيل الدالة الرئيسية
fetchAllPages().catch(error => {
  console.error('❌ خطأ عام:', error);
});
