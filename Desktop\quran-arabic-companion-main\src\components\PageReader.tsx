import React, { useState, useEffect, useMemo, useRef } from 'react';
import { ChevronLeft, ChevronRight, BookOpen, Settings, ArrowRight, X, Bookmark, HelpCircle } from 'lucide-react';
import surahsInfo from '../data/surah.json';
import quranData from '../data/quran.json';
import { useSwipeable } from 'react-swipeable';
import QuranPageViewer from './QuranPageViewer';

interface PageData {
  page: number;
  juz: number;
  surahs: Array<{
    surah: number;
    name: string;
    verses: number[];
  }>;
}

interface TajweedRule {
  rule: string;
  start: number;
  end: number;
}

interface Verse {
  key: string;
  text: string;
  surah: number;
  number: number;
  tajweed: TajweedRule[];
}

interface PageReaderProps {
  initialPage?: number;
  onBack?: () => void;
}

const TOTAL_PAGES = 604;

const PageReader: React.FC<PageReaderProps> = ({ initialPage = 1, onBack }) => {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [verses, setVerses] = useState<Verse[]>([]);
  const [loading, setLoading] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const [fontSize, setFontSize] = useState(24);
  const [mushafType, setMushafType] = useState<'connected' | 'separated'>('connected');
  const [textAlignment, setTextAlignment] = useState<'justified' | 'right'>('justified');
  const [verseNumberStyle, setVerseNumberStyle] = useState<'circle' | 'square' | 'ornate'>('ornate');
  const [colorTheme, setColorTheme] = useState<'green' | 'blue' | 'gold' | 'dark'>('green');
  const [darkMode, setDarkMode] = useState<'dark' | 'light' | 'auto'>('dark');
  const [numberFormat, setNumberFormat] = useState<'arabic' | 'english'>('arabic');
  const [touchStartX, setTouchStartX] = useState<number | null>(null);
  const [showGuide, setShowGuide] = useState(false);
  const [showPageJump, setShowPageJump] = useState(false);
  const [jumpToPage, setJumpToPage] = useState('');
  const [showVerseMenu, setShowVerseMenu] = useState(false);
  const [selectedVerse, setSelectedVerse] = useState<{verse: Verse, index: number} | null>(null);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [colorPickerType, setColorPickerType] = useState<'verse' | 'page'>('verse');
  const [showVerseColors, setShowVerseColors] = useState(false);
  const [bookmarkSaved, setBookmarkSaved] = useState<string | null>(null);
  const [longPressActive, setLongPressActive] = useState(false);
  const [savedVerseBookmarks, setSavedVerseBookmarks] = useState<any[]>([]);
  const [pagesData, setPagesData] = useState<PageData[]>([]);

  const totalPages = pagesData.length;

  const pageData = useMemo(() => pagesData.find(p => p.page === currentPage), [currentPage, pagesData]);

  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const response = await fetch('/src/data/pages_corrected.json');
        let data = await response.json();
        
        // Apply patch for page 604
        const page604Index = data.findIndex((p: PageData) => p.page === 604);
        if (page604Index > -1) {
          data[page604Index].surahs = [
            { "surah": 112, "name": "الإخلاص", "verses": [ 1, 2, 3, 4 ] },
            { "surah": 113, "name": "الفلق", "verses": [ 1, 2, 3, 4, 5 ] },
            { "surah": 114, "name": "الناس", "verses": [ 1, 2, 3, 4, 5, 6 ] }
          ];
        }

        setPagesData(data);
      } catch (error) {
        console.error("Failed to load pages data", error);
      }
    };
    loadInitialData();
  }, []);

  useEffect(() => {
    const loadPageData = async () => {
      if (!pageData) return;
      setLoading(true);

      const requiredSurahIds = new Set(pageData.surahs.map(s => s.surah));
      const tajweedCache = new Map<number, any>();

      const tajweedPromises = Array.from(requiredSurahIds).map(id =>
        fetch(`/src/data/tajweed/surah_${id}.json`)
          .then(res => res.ok ? res.json() : null)
          .then(data => ({ id, data }))
      );
      
      const tajweedResults = await Promise.all(tajweedPromises);
      tajweedResults.forEach(result => {
        if (result && result.data) {
          tajweedCache.set(result.id, result.data);
        }
      });
      
      const pageVerses: Verse[] = [];
      pageData.surahs.forEach(surahInfo => {
        const surahContent = quranData.find(s => s.id === surahInfo.surah);
        const tajweedData = tajweedCache.get(surahInfo.surah);

        if (surahContent) {
          surahInfo.verses.forEach(verseNumber => {
            const verseContent = surahContent.verses.find(v => v.id === verseNumber);
            if (verseContent) {
              pageVerses.push({
                key: `${surahInfo.surah}:${verseNumber}`,
                text: verseContent.text,
                surah: surahInfo.surah,
                number: verseNumber,
                tajweed: tajweedData?.verse?.[`verse_${verseNumber}`] || []
              });
            }
          });
        }
      });
      
      setVerses(pageVerses);
      setLoading(false);
    };

    if (pagesData.length > 0) {
      loadPageData();
    }
  }, [pageData, pagesData]);

  // Load settings and bookmarks from localStorage
  useEffect(() => {
    const savedFontSize = localStorage.getItem('quran-font-size');
    const savedMushafType = localStorage.getItem('quran-mushaf-type');
    const savedTextAlignment = localStorage.getItem('quran-text-alignment');
    const savedVerseNumberStyle = localStorage.getItem('quran-verse-number-style');
    const savedColorTheme = localStorage.getItem('quran-color-theme');
    const savedDarkMode = localStorage.getItem('quran-dark-mode');
    const savedNumberFormat = localStorage.getItem('quran-number-format');

    if (savedFontSize) setFontSize(parseInt(savedFontSize));
    if (savedMushafType) setMushafType(savedMushafType as 'connected' | 'separated');
    if (savedTextAlignment) setTextAlignment(savedTextAlignment as 'justified' | 'right');
    if (savedVerseNumberStyle) setVerseNumberStyle(savedVerseNumberStyle as 'circle' | 'square' | 'ornate');
    if (savedColorTheme) setColorTheme(savedColorTheme as 'green' | 'blue' | 'gold' | 'dark');
    if (savedDarkMode) setDarkMode(savedDarkMode as 'dark' | 'light' | 'auto');
    if (savedNumberFormat) setNumberFormat(savedNumberFormat as 'arabic' | 'english');

    // Load saved verse bookmarks
    const savedBookmarks = JSON.parse(localStorage.getItem('lastReadAyas') || '[]');
    setSavedVerseBookmarks(savedBookmarks);
  }, []);

  // Save verse as bookmark with color
  const saveVerseBookmark = (verse: Verse, color: string) => {
    const currentSurah = surahsInfo.find(s => parseInt(s.index) === verse.surah);

    const newBookmark = {
      id: `${verse.key}_${Date.now()}`,
      surahName: currentSurah?.titleAr || '',
      ayaNumber: verse.number,
      pageNumber: currentPage,
      timestamp: new Date().toISOString(),
      color: color,
      verseText: verse.text.substring(0, 50) + '...' // First 50 chars
    };

    // Get existing bookmarks
    const existingBookmarks = JSON.parse(localStorage.getItem('lastReadAyas') || '[]');

    // Add new bookmark - unlimited
    const updatedBookmarks = [newBookmark, ...existingBookmarks];

    // Save updated bookmarks
    localStorage.setItem('lastReadAyas', JSON.stringify(updatedBookmarks));
    setSavedVerseBookmarks(updatedBookmarks); // Update state

    // Also save as single lastReadAya for compatibility
    const lastReadAya = {
      surahName: currentSurah?.titleAr || '',
      ayaNumber: verse.number,
      pageNumber: currentPage,
      timestamp: new Date().toISOString()
    };
    localStorage.setItem('lastReadAya', JSON.stringify(lastReadAya));

    setBookmarkSaved('verse');
    setTimeout(() => setBookmarkSaved(null), 2000);
  };

  // Show color picker for page
  const showPageColorPicker = () => {
    setColorPickerType('page');
    setShowColorPicker(true);
  };

  // Remove verse bookmark
  const removeVerseBookmark = (verse: Verse, color: string) => {
    // Get existing bookmarks
    const existingBookmarks = JSON.parse(localStorage.getItem('lastReadAyas') || '[]');

    // Remove bookmark with same verse and color
    const updatedBookmarks = existingBookmarks.filter((bookmark: any) =>
      !(bookmark.ayaNumber === verse.number &&
        bookmark.pageNumber === currentPage &&
        bookmark.color === color)
    );

    // Save updated bookmarks
    localStorage.setItem('lastReadAyas', JSON.stringify(updatedBookmarks));
    setSavedVerseBookmarks(updatedBookmarks);

    setBookmarkSaved('removed');
    setTimeout(() => setBookmarkSaved(null), 2000);
  };

  // Get current verse bookmark color
  const getVerseBookmarkColor = (verseKey: string) => {
    const bookmarkedVerse = savedVerseBookmarks.find(bookmark =>
      bookmark.ayaNumber === parseInt(verseKey.split(':')[1]) &&
      bookmark.pageNumber === currentPage
    );
    return bookmarkedVerse?.color || null;
  };

  // Remove page bookmark
  const removePageBookmark = (color: string) => {
    // Get existing bookmarks
    const existingBookmarks = JSON.parse(localStorage.getItem('lastReadPages') || '[]');

    // Remove bookmark with same page and color
    const updatedBookmarks = existingBookmarks.filter((bookmark: any) =>
      !(bookmark.pageNumber === currentPage && bookmark.color === color)
    );

    // Save updated bookmarks
    localStorage.setItem('lastReadPages', JSON.stringify(updatedBookmarks));

    setBookmarkSaved('removed');
    setTimeout(() => setBookmarkSaved(null), 2000);
  };

  // Get current page bookmark color
  const getPageBookmarkColor = () => {
    const existingBookmarks = JSON.parse(localStorage.getItem('lastReadPages') || '[]');
    const pageBookmark = existingBookmarks.find((bookmark: any) =>
      bookmark.pageNumber === currentPage
    );
    return pageBookmark?.color || null;
  };

  // Share verse
  const shareVerse = () => {
    if (selectedVerse) {
      const shareText = `${selectedVerse.verse.text}\n\n(${selectedVerse.verse.key}) - القرآن الكريم`;
      if (navigator.share) {
        navigator.share({
          title: 'آية من القرآن الكريم',
          text: shareText
        });
      } else {
        navigator.clipboard.writeText(shareText);
        setBookmarkSaved('shared');
        setTimeout(() => setBookmarkSaved(null), 2000);
      }
    }
    setShowVerseMenu(false);
  };

  // Share page
  const sharePage = () => {
    const pageText = verses.map(v => v.text).join('\n\n');
    const shareText = `${pageText}\n\nصفحة ${currentPage} - القرآن الكريم`;
    if (navigator.share) {
      navigator.share({
        title: 'صفحة من القرآن الكريم',
        text: shareText
      });
    } else {
      navigator.clipboard.writeText(shareText);
      setBookmarkSaved('shared');
      setTimeout(() => setBookmarkSaved(null), 2000);
    }
    setShowVerseMenu(false);
  };

  // Quick page jump function
  const handlePageJump = () => {
    const pageNum = parseInt(jumpToPage);
    if (pageNum >= 1 && pageNum <= totalPages) {
      setCurrentPage(pageNum);
      setShowPageJump(false);
      setJumpToPage('');
    }
  };

  // Save settings to localStorage
  useEffect(() => {
    localStorage.setItem('quran-font-size', fontSize.toString());
    localStorage.setItem('quran-mushaf-type', mushafType);
    localStorage.setItem('quran-text-alignment', textAlignment);
    localStorage.setItem('quran-verse-number-style', verseNumberStyle);
    localStorage.setItem('quran-color-theme', colorTheme);
    localStorage.setItem('quran-dark-mode', darkMode);
    localStorage.setItem('quran-number-format', numberFormat);
  }, [fontSize, mushafType, textAlignment, verseNumberStyle, colorTheme, darkMode, numberFormat]);

  // حفظ آخر صفحة في localStorage
  useEffect(() => {
    localStorage.setItem('lastReadPage', currentPage.toString());
  }, [currentPage]);

  // تحميل آخر صفحة عند بدء التطبيق
  useEffect(() => {
    const lastPage = localStorage.getItem('lastReadPage');
    if (lastPage && !initialPage) {
      setCurrentPage(parseInt(lastPage));
    }
  }, []);

  // حفظ معلومات إضافية عن القراءة
  useEffect(() => {
    if (pageData) {
      const readingInfo = {
        page: currentPage,
        juz: pageData.juz,
        surah: pageData.surahs[0]?.name || '',
        timestamp: new Date().toISOString(),
        progress: Math.round((currentPage / totalPages) * 100)
      };
      localStorage.setItem('lastReadingInfo', JSON.stringify(readingInfo));
    }
  }, [currentPage, pageData, totalPages]);

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToPage = (pageNum: number) => {
    if (pageNum >= 1 && pageNum <= totalPages) {
      setCurrentPage(pageNum);
    }
  };

  // Touch navigation functions
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX);
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (touchStartX === null) return;
    const touchEndX = e.changedTouches[0].clientX;
    const diff = touchEndX - touchStartX;
    if (diff > 50 && currentPage > 1) {
      setCurrentPage(currentPage - 1); // سحب يمين: الصفحة السابقة
    } else if (diff < -50 && currentPage < TOTAL_PAGES) {
      setCurrentPage(currentPage + 1); // سحب يسار: الصفحة التالية
    }
    setTouchStartX(null);
  };

  const formatVerseNumber = (verseKey: string) => {
    const num = verseKey.split(':')[1];
    return convertNumber(num);
  };

  const convertNumber = (num: string | number) => {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const numStr = num.toString();

    if (numberFormat === 'arabic') {
      return numStr.split('').map(digit =>
        /\d/.test(digit) ? arabicNumbers[parseInt(digit)] : digit
      ).join('');
    }
    return numStr;
  };

  const getVerseNumberStyle = (verseKey: string) => {
    // Check if this verse is bookmarked
    const bookmarkedVerse = savedVerseBookmarks.find(bookmark =>
      bookmark.ayaNumber === parseInt(verseKey.split(':')[1]) &&
      bookmark.pageNumber === currentPage
    );

    let style: React.CSSProperties = {};
    let classes = "verse-number-traditional";

    if (bookmarkedVerse) {
      // Use the saved color for bookmarked verses
      style.backgroundColor = bookmarkedVerse.color;
      style.borderColor = 'white';
      style.color = 'white';
      classes += ' bookmarked-verse';
    }

    // Apply different styles based on user preference
    switch (verseNumberStyle) {
      case 'circle':
        classes = bookmarkedVerse ? classes : 'verse-number-traditional';
        break;
      case 'square':
        classes = bookmarkedVerse ? classes : 'verse-number-traditional rounded-lg';
        break;
      case 'ornate':
        classes = bookmarkedVerse ? classes : 'verse-number-traditional w-10 h-10 ornate-number';
        break;
      default:
        classes = bookmarkedVerse ? classes : 'verse-number-traditional';
    }

    return { className: classes, style };
  };

  const getThemeColors = () => {
    switch (colorTheme) {
      case 'blue':
        return {
          primary: 'from-blue-600 to-blue-500',
          secondary: 'from-blue-500 to-blue-400',
          accent: 'border-blue-300',
          verseNumberColor: 'text-blue-300',
          surahHeaderColor: 'text-blue-300',
          surahHeaderBg: 'bg-blue-700/50'
        };
      case 'gold':
        return {
          primary: 'from-amber-600 to-amber-500',
          secondary: 'from-amber-500 to-amber-400',
          accent: 'border-amber-300',
          verseNumberColor: 'text-amber-300',
          surahHeaderColor: 'text-amber-300',
          surahHeaderBg: 'bg-amber-700/50'
        };
      case 'dark':
        return {
          primary: 'from-gray-600 to-gray-500',
          secondary: 'from-gray-500 to-gray-400',
          accent: 'border-gray-300',
          verseNumberColor: 'text-gray-300',
          surahHeaderColor: 'text-gray-300',
          surahHeaderBg: 'bg-gray-700/50'
        };
      default: // green
        return {
          primary: 'from-green-600 to-green-500',
          secondary: 'from-green-500 to-green-400',
          accent: 'border-green-300',
          verseNumberColor: 'text-green-300',
          surahHeaderColor: 'text-green-300',
          surahHeaderBg: 'bg-green-700/50'
        };
    }
  };

  const shouldShowBismillah = () => {
    if (currentPage === 1) return false;
    const firstVerseOnPage = pageData.surahs[0]?.verses[0];
    const firstSurahOnPage = pageData.surahs[0]?.surah;
    return firstVerseOnPage === 1 && firstSurahOnPage !== 1 && firstSurahOnPage !== 9;
  };

  const getSurahHeader = () => {
    if (!pageData) return null;
    const firstVerseOnPage = pageData.surahs[0]?.verses[0];
    
    // Show header for the first surah on the page if it's the beginning of that surah
    if (firstVerseOnPage === 1) {
      const surah = surahsInfo.find(s => parseInt(s.index) === pageData.surahs[0].surah);
      if (surah) {
        return (
          <div className={`text-center my-4 p-2 rounded-lg ${getThemeColors().surahHeaderBg}`}>
            <h2 className={`text-2xl font-bold ${getThemeColors().surahHeaderColor}`}>سورة {surah.titleAr}</h2>
          </div>
        );
      }
    }
    
    // Check if a new surah starts mid-page
    for (let i = 0; i < verses.length - 1; i++) {
      if (verses[i].surah !== verses[i + 1].surah) {
        const newSurah = surahsInfo.find(s => parseInt(s.index) === verses[i + 1].surah);
        if (newSurah) {
          // This is complex to inject mid-render. For now, we handle the page start case.
          // A better approach would be to restructure the render logic.
        }
      }
    }
    
    return null;
  };

  const getBackgroundStyle = () => {
    switch (darkMode) {
      case 'light':
        return 'bg-gradient-to-b from-gray-100 to-gray-200 text-gray-900';
      case 'dark':
        return 'bg-gradient-to-b from-gray-900 to-gray-800 text-white';
      case 'auto':
        // يمكن إضافة logic للكشف عن وضع النظام
        return 'bg-gradient-to-b from-gray-900 to-gray-800 text-white';
      default:
        return 'bg-gradient-to-b from-gray-900 to-gray-800 text-white';
    }
  };

  const getContentBackgroundStyle = () => {
    switch (darkMode) {
      case 'light':
        return 'bg-gradient-to-b from-white to-gray-50';
      case 'dark':
        return 'bg-gradient-to-b from-gray-800 to-gray-700';
      case 'auto':
        return 'bg-gradient-to-b from-gray-800 to-gray-700';
      default:
        return 'bg-gradient-to-b from-gray-800 to-gray-700';
    }
  };

  const tajweedColors: { [key: string]: string } = {
    'hamzat_wasl': 'text-gray-400',
    'lam_shamsiyyah': 'text-sky-500',
    'madd_2': 'text-red-500',
    'madd_6': 'text-purple-600',
    'madd_246': 'text-orange-500',
    'ghunnah': 'text-green-600',
    'idghaam_ghunnah': 'text-teal-600',
    'idghaam_no_ghunnah': 'text-blue-600',
    'ikhfaa': 'text-cyan-600',
    'iqlab': 'text-indigo-500',
    'qalqalah': 'font-bold', // Will use the theme's text color
  };

  const renderVerseWithTajweed = (verse: Verse) => {
    const { text, tajweed } = verse;
    if (!tajweed || tajweed.length === 0) {
      return <span>{text}</span>;
    }

    const segments = [];
    let lastIndex = 0;

    const sortedTajweed = [...tajweed].sort((a, b) => a.start - b.start);

    sortedTajweed.forEach((rule, i) => {
      if (rule.start > lastIndex) {
        segments.push(<span key={`text-${i}-pre`}>{text.substring(lastIndex, rule.start)}</span>);
      }

      const ruleText = text.substring(rule.start, rule.end);
      const colorClass = tajweedColors[rule.rule] || '';
      segments.push(<span key={`rule-${i}`} className={colorClass}>{ruleText}</span>);

      lastIndex = rule.end;
    });

    if (lastIndex < text.length) {
      segments.push(<span key="text-last">{text.substring(lastIndex)}</span>);
    }

    return <>{segments}</>;
  };

  // Swipe handlers
  const handlers = useSwipeable({
    onSwipedLeft: () => currentPage < TOTAL_PAGES && setCurrentPage(currentPage + 1),
    onSwipedRight: () => currentPage > 1 && setCurrentPage(currentPage - 1),
    trackMouse: true,
  });

  // اسم الصورة بناءً على رقم الصفحة
  const paddedPage = currentPage.toString().padStart(4, '0');
  const imageUrl = `/images_cropped1/${paddedPage}.jpg`;

  return (
    <QuranPageViewer
      pageNumber={currentPage}
      onBack={onBack || (() => {})}
      onPageChange={setCurrentPage}
      totalPages={604}
      // يمكن لاحقًا تمرير اسم السورة/الجزء من البيانات لو أردنا
    />
  );
};

export default PageReader;

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
  .long-press-active {
    animation: longPressGlow 0.8s ease-in-out;
    background: rgba(59, 130, 246, 0.2) !important;
    border-radius: 8px;
    transform: scale(1.02);
  }

  @keyframes longPressGlow {
    0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4); }
    50% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0.2); }
    100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  @keyframes slideUp {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  .verse-text:active,
  .verse-card:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
`;

if (!document.head.querySelector('#page-reader-styles')) {
  style.id = 'page-reader-styles';
  document.head.appendChild(style);
}
