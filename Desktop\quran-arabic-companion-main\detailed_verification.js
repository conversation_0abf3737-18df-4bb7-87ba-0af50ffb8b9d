import fs from 'fs';

// قراءة البيانات
const surahData = JSON.parse(fs.readFileSync('./src/data/surah.json', 'utf8'));
const juzData = JSON.parse(fs.readFileSync('./src/data/juz.json', 'utf8'));
const pagesData = JSON.parse(fs.readFileSync('./src/data/pages.json', 'utf8'));

console.log('🔍 فحص تفصيلي لربط السور والأجزاء والصفحات...\n');

// 1. التحقق من سورة آل عمران تحديداً
console.log('1️⃣ فحص سورة آل عمران:');
const aalImranData = surahData.find(s => s.index === "003");
if (aalImranData) {
  console.log(`✅ سورة آل عمران موجودة:`);
  console.log(`   - الاسم: ${aalImranData.titleAr}`);
  console.log(`   - عدد الآيات: ${aalImranData.count}`);
  console.log(`   - الصفحة: ${aalImranData.pages}`);
  console.log(`   - الأجزاء: ${aalImranData.juz.map(j => j.index).join(', ')}`);
  
  // فحص ملف السورة
  try {
    const surahFile = JSON.parse(fs.readFileSync('./src/data/surah/surah_3.json', 'utf8'));
    const verseCount = Object.keys(surahFile.verse).filter(key => key !== 'verse_0').length;
    console.log(`   - عدد الآيات في الملف: ${verseCount}`);
    
    if (verseCount === aalImranData.count) {
      console.log('   ✅ عدد الآيات متطابق');
    } else {
      console.log('   ❌ عدد الآيات غير متطابق');
    }
  } catch (error) {
    console.log(`   ❌ خطأ في قراءة ملف السورة: ${error.message}`);
  }
} else {
  console.log('❌ سورة آل عمران غير موجودة في البيانات');
}

// 2. فحص الصفحات التي تحتوي على سورة آل عمران
console.log('\n2️⃣ فحص الصفحات التي تحتوي على سورة آل عمران:');
const aalImranPages = pagesData.filter(page => 
  page.surahs.some(surah => surah.surah === 3)
);

console.log(`عدد الصفحات التي تحتوي على سورة آل عمران: ${aalImranPages.length}`);
aalImranPages.slice(0, 5).forEach(page => {
  const aalImranSurah = page.surahs.find(s => s.surah === 3);
  console.log(`   صفحة ${page.page}: آيات ${aalImranSurah.verses.join(', ')}`);
});

// 3. فحص بداية كل جزء
console.log('\n3️⃣ فحص بداية الأجزاء:');
for (let i = 0; i < Math.min(10, juzData.length); i++) {
  const juz = juzData[i];
  console.log(`الجزء ${juz.index}: يبدأ من سورة ${juz.start.name} آية ${juz.start.verse}`);
  
  // البحث عن الصفحة التي تحتوي على بداية هذا الجزء
  const startPage = pagesData.find(page => 
    page.juz === parseInt(juz.index) && 
    page.surahs.some(surah => 
      surah.surah === parseInt(juz.start.index) && 
      surah.verses.includes(parseInt(juz.start.verse.replace('verse_', '')))
    )
  );
  
  if (startPage) {
    console.log(`   ✅ يبدأ في الصفحة ${startPage.page}`);
  } else {
    console.log(`   ❌ لم يتم العثور على الصفحة المناسبة`);
  }
}

// 4. فحص عينة من الصفحات للتأكد من صحة المحتوى
console.log('\n4️⃣ فحص عينة من الصفحات:');
const samplePages = [1, 50, 100, 200, 300, 400, 500, 604];

for (const pageNum of samplePages) {
  if (pageNum <= pagesData.length) {
    const page = pagesData[pageNum - 1];
    console.log(`صفحة ${page.page}:`);
    console.log(`   - الجزء: ${page.juz}`);
    console.log(`   - السور: ${page.surahs.map(s => `${s.name} (${s.surah})`).join(', ')}`);
    
    // التحقق من صحة أرقام الآيات
    let validVerses = true;
    for (const surah of page.surahs) {
      const surahInfo = surahData.find(s => s.index === surah.surah.toString().padStart(3, '0'));
      if (surahInfo) {
        const maxVerse = surahInfo.count;
        const invalidVerses = surah.verses.filter(v => v > maxVerse || v < 1);
        if (invalidVerses.length > 0) {
          console.log(`   ❌ آيات خاطئة في سورة ${surah.name}: ${invalidVerses.join(', ')}`);
          validVerses = false;
        }
      }
    }
    
    if (validVerses) {
      console.log(`   ✅ أرقام الآيات صحيحة`);
    }
  }
}

// 5. فحص تطابق بيانات الأجزاء مع الصفحات
console.log('\n5️⃣ فحص تطابق بيانات الأجزاء مع الصفحات:');
let juzPageMatches = 0;
let juzPageMismatches = 0;

for (const juz of juzData.slice(0, 5)) { // فحص أول 5 أجزاء
  const juzNumber = parseInt(juz.index);
  const juzPages = pagesData.filter(page => page.juz === juzNumber);
  
  console.log(`الجزء ${juzNumber}: ${juzPages.length} صفحة`);
  
  if (juzPages.length > 0) {
    const firstPage = juzPages[0];
    const lastPage = juzPages[juzPages.length - 1];
    console.log(`   من الصفحة ${firstPage.page} إلى الصفحة ${lastPage.page}`);
    juzPageMatches++;
  } else {
    console.log(`   ❌ لا توجد صفحات للجزء ${juzNumber}`);
    juzPageMismatches++;
  }
}

console.log(`\n📊 نتائج فحص الأجزاء: ${juzPageMatches} صحيح، ${juzPageMismatches} خاطئ`);

console.log('\n🎉 انتهى الفحص التفصيلي!');
