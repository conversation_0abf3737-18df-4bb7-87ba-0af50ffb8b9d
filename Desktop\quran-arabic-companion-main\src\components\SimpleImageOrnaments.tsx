import React from 'react';

// مكون بسيط لاستخدام الصورة الأولى (الإطار الذهبي)
interface SimpleSurahHeaderProps {
  surahNameArabic: string;
  surahNameEnglish: string;
  surahNumber?: number;
}

export const SimpleSurahHeader: React.FC<SimpleSurahHeaderProps> = ({
  surahNameArabic,
  surahNameEnglish,
  surahNumber
}) => {
  return (
    <div className="surah-header-original">
      <div className="content">
        <div className="surah-name-arabic">{surahNameArabic}</div>
        <div className="surah-name-english">
          {surahNameEnglish}
          {surahNumber && ` • ${surahNumber}`}
        </div>
      </div>
    </div>
  );
};

// مكون بسيط لاستخدام الصورة الثانية (إطار الصفحة)
interface SimpleQuranPageProps {
  children: React.ReactNode;
}

export const SimpleQuranPage: React.FC<SimpleQuranPageProps> = ({ children }) => {
  return (
    <div className="quran-page-original">
      <div className="page-content">
        {children}
      </div>
    </div>
  );
};

// مكون مدمج يستخدم الصورتين معاً
interface SimpleIslamicLayoutProps {
  surahNameArabic: string;
  surahNameEnglish: string;
  surahNumber: number;
  verses: Array<{
    number: number;
    text: string;
  }>;
  showBismillah?: boolean;
}

export const SimpleIslamicLayout: React.FC<SimpleIslamicLayoutProps> = ({
  surahNameArabic,
  surahNameEnglish,
  surahNumber,
  verses,
  showBismillah = true
}) => {
  return (
    <div className="combined-layout">
      {/* رأس السورة بالصورة الأولى */}
      <SimpleSurahHeader
        surahNameArabic={surahNameArabic}
        surahNameEnglish={surahNameEnglish}
        surahNumber={surahNumber}
      />
      
      {/* محتوى الصفحة بالصورة الثانية */}
      <SimpleQuranPage>
        {/* البسملة */}
        {showBismillah && surahNumber !== 1 && surahNumber !== 9 && (
          <div className="bismillah-in-frame">
            بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
          </div>
        )}
        
        {/* الآيات */}
        <div className="verses-in-frame">
          {verses.map((verse) => (
            <div key={verse.number} className="verse-in-frame">
              {verse.text}
              <span className="verse-number-in-frame">{verse.number}</span>
            </div>
          ))}
        </div>
      </SimpleQuranPage>
    </div>
  );
};

// مثال للاستخدام
export const ExampleUsage: React.FC = () => {
  const exampleVerses = [
    { number: 1, text: "الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ" },
    { number: 2, text: "الرَّحْمَٰنِ الرَّحِيمِ" },
    { number: 3, text: "مَالِكِ يَوْمِ الدِّينِ" }
  ];

  return (
    <div className="p-8">
      <h1 className="text-3xl font-bold text-center mb-8">
        مثال استخدام الصور الأصلية
      </h1>
      
      <SimpleIslamicLayout
        surahNameArabic="الفاتحة"
        surahNameEnglish="Al-Fatiha"
        surahNumber={1}
        verses={exampleVerses}
        showBismillah={false} // الفاتحة لا تحتاج بسملة منفصلة
      />
    </div>
  );
};

export default {
  SimpleSurahHeader,
  SimpleQuranPage,
  SimpleIslamicLayout,
  ExampleUsage
};
