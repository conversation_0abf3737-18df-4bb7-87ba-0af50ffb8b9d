import fs from 'fs';

console.log('📄 إنشاء ملف pages.json مكتمل...\n');

// قراءة بيانات السور
const surahData = JSON.parse(fs.readFileSync('./src/data/surah.json', 'utf8'));

// إنشاء صفحات مكتملة (604 صفحة)
const completePages = [];

// خريطة السور والصفحات (حسب المصحف الشريف)
const surahPages = {
  1: { start: 1, end: 1 },      // الفاتحة
  2: { start: 2, end: 49 },     // البقرة
  3: { start: 50, end: 76 },    // آل عمران
  4: { start: 77, end: 106 },   // النساء
  5: { start: 106, end: 127 },  // المائدة
  6: { start: 128, end: 150 },  // الأنعام
  7: { start: 151, end: 176 },  // الأعراف
  8: { start: 177, end: 187 },  // الأنفال
  9: { start: 187, end: 207 },  // التوبة
  10: { start: 208, end: 221 }, // يونس
  // ... سنضيف باقي السور
};

// إنشاء الصفحات الأساسية (أول 20 صفحة كمثال)
for (let page = 1; page <= 604; page++) {
  let juzNumber = Math.ceil(page / 20); // تقريبي
  if (juzNumber > 30) juzNumber = 30;
  
  // تحديد السور في هذه الصفحة
  let surahs = [];
  
  if (page === 1) {
    // الصفحة الأولى - سورة الفاتحة
    surahs = [{
      surah: 1,
      name: "الفاتحة",
      verses: [1, 2, 3, 4, 5, 6, 7]
    }];
  } else if (page >= 2 && page <= 49) {
    // صفحات سورة البقرة
    const versesPerPage = Math.ceil(286 / 48); // تقسيم آيات البقرة على 48 صفحة
    const startVerse = (page - 2) * versesPerPage + 1;
    const endVerse = Math.min(startVerse + versesPerPage - 1, 286);
    
    const verses = [];
    for (let v = startVerse; v <= endVerse; v++) {
      verses.push(v);
    }
    
    surahs = [{
      surah: 2,
      name: "البقرة",
      verses: verses
    }];
  } else if (page >= 50 && page <= 76) {
    // صفحات سورة آل عمران
    const versesPerPage = Math.ceil(200 / 27);
    const startVerse = (page - 50) * versesPerPage + 1;
    const endVerse = Math.min(startVerse + versesPerPage - 1, 200);
    
    const verses = [];
    for (let v = startVerse; v <= endVerse; v++) {
      verses.push(v);
    }
    
    surahs = [{
      surah: 3,
      name: "آل عمران",
      verses: verses
    }];
  } else if (page >= 582 && page <= 604) {
    // الجزء الثلاثون - السور القصيرة (تحديث دقيق)
    const shortSurahs = [
      { id: 78, name: "النبأ", verses: 40, startPage: 582, endPage: 583 },
      { id: 79, name: "النازعات", verses: 46, startPage: 583, endPage: 585 },
      { id: 80, name: "عبس", verses: 42, startPage: 585, endPage: 586 },
      { id: 81, name: "التكوير", verses: 29, startPage: 586, endPage: 587 },
      { id: 82, name: "الانفطار", verses: 19, startPage: 587, endPage: 587 },
      { id: 83, name: "المطففين", verses: 36, startPage: 587, endPage: 589 },
      { id: 84, name: "الانشقاق", verses: 25, startPage: 589, endPage: 590 },
      { id: 85, name: "البروج", verses: 22, startPage: 590, endPage: 590 },
      { id: 86, name: "الطارق", verses: 17, startPage: 591, endPage: 591 },
      { id: 87, name: "الأعلى", verses: 19, startPage: 591, endPage: 592 },
      { id: 88, name: "الغاشية", verses: 26, startPage: 592, endPage: 592 },
      { id: 89, name: "الفجر", verses: 30, startPage: 593, endPage: 594 },
      { id: 90, name: "البلد", verses: 20, startPage: 594, endPage: 594 },
      { id: 91, name: "الشمس", verses: 15, startPage: 595, endPage: 595 },
      { id: 92, name: "الليل", verses: 21, startPage: 595, endPage: 596 },
      { id: 93, name: "الضحى", verses: 11, startPage: 596, endPage: 596 },
      { id: 94, name: "الشرح", verses: 8, startPage: 596, endPage: 596 },
      { id: 95, name: "التين", verses: 8, startPage: 597, endPage: 597 },
      { id: 96, name: "العلق", verses: 19, startPage: 597, endPage: 598 },
      { id: 97, name: "القدر", verses: 5, startPage: 598, endPage: 598 },
      { id: 98, name: "البينة", verses: 8, startPage: 598, endPage: 599 },
      { id: 99, name: "الزلزلة", verses: 8, startPage: 599, endPage: 599 },
      { id: 100, name: "العاديات", verses: 11, startPage: 599, endPage: 600 },
      { id: 101, name: "القارعة", verses: 11, startPage: 600, endPage: 600 },
      { id: 102, name: "التكاثر", verses: 8, startPage: 600, endPage: 601 },
      { id: 103, name: "العصر", verses: 3, startPage: 601, endPage: 601 },
      { id: 104, name: "الهمزة", verses: 9, startPage: 601, endPage: 602 },
      { id: 105, name: "الفيل", verses: 5, startPage: 602, endPage: 602 },
      { id: 106, name: "قريش", verses: 4, startPage: 602, endPage: 602 },
      { id: 107, name: "الماعون", verses: 7, startPage: 603, endPage: 603 },
      { id: 108, name: "الكوثر", verses: 3, startPage: 603, endPage: 603 },
      { id: 109, name: "الكافرون", verses: 6, startPage: 603, endPage: 604 },
      { id: 110, name: "النصر", verses: 3, startPage: 604, endPage: 604 },
      { id: 111, name: "المسد", verses: 5, startPage: 604, endPage: 604 },
      { id: 112, name: "الإخلاص", verses: 4, startPage: 604, endPage: 604 },
      { id: 113, name: "الفلق", verses: 5, startPage: 604, endPage: 604 },
      { id: 114, name: "الناس", verses: 6, startPage: 604, endPage: 604 }
    ];

    // البحث عن السور في هذه الصفحة
    const pageSurahs = shortSurahs.filter(s => page >= s.startPage && page <= s.endPage);

    surahs = pageSurahs.map(s => {
      // تحديد الآيات المناسبة لهذه الصفحة
      let verses = [];
      if (s.startPage === s.endPage) {
        // السورة في صفحة واحدة
        for (let v = 1; v <= s.verses; v++) {
          verses.push(v);
        }
      } else {
        // السورة موزعة على عدة صفحات
        const versesPerPage = Math.ceil(s.verses / (s.endPage - s.startPage + 1));
        const pageIndex = page - s.startPage;
        const startVerse = pageIndex * versesPerPage + 1;
        const endVerse = Math.min(startVerse + versesPerPage - 1, s.verses);

        for (let v = startVerse; v <= endVerse; v++) {
          verses.push(v);
        }
      }

      return {
        surah: s.id,
        name: s.name,
        verses: verses
      };
    });
  } else {
    // صفحات أخرى - سنضع بيانات تقريبية
    surahs = [{
      surah: Math.ceil(page / 5), // تقريبي
      name: "سورة تقريبية",
      verses: [1, 2, 3, 4, 5]
    }];
  }
  
  completePages.push({
    page: page,
    juz: juzNumber,
    surahs: surahs
  });
}

// حفظ الملف
fs.writeFileSync('./src/data/pages.json', JSON.stringify(completePages, null, 2), 'utf8');

console.log(`✅ تم إنشاء ملف pages.json مع ${completePages.length} صفحة`);
console.log('📝 ملاحظة: البيانات تحتاج إلى تحديث دقيق حسب المصحف الشريف');
console.log('🔄 يرجى مراجعة البيانات وتحديثها حسب الحاجة');
