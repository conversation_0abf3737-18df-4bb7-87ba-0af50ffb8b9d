
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Scheherazade+New:wght@400;500;600;700&display=swap');
@import './styles/islamic-ornaments.css';
@import './styles/original-images.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* تحسينات فواصل السور */
.surah-separator {
  position: relative;
  height: 2px;
  background: linear-gradient(90deg, transparent, #10B981, transparent);
  margin: 2rem 0;
}

.surah-separator::before,
.surah-separator::after {
  content: '';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background: #10B981;
  border-radius: 50%;
}

.surah-separator::before {
  left: 20%;
}

.surah-separator::after {
  right: 20%;
}

/* تحسين عرض اسم السورة */
.surah-header {
  position: relative;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #059669, #10B981);
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.surah-header::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #10B981, #059669, #10B981);
  border-radius: 1rem;
  z-index: -1;
}

/* تحسين البسملة */
.bismillah-container {
  position: relative;
  background: linear-gradient(135deg, #3B82F6, #8B5CF6);
  border-radius: 0.75rem;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.bismillah-container::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, #3B82F6, #8B5CF6, #3B82F6);
  border-radius: 0.75rem;
  z-index: -1;
}

/* تحسين انتقالات الصفحات */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسين أرقام الآيات */
.verse-number-ornate {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, #F59E0B, #D97706);
  border-radius: 50%;
  color: white;
  font-weight: bold;
  font-size: 0.875rem;
  margin: 0 0.5rem;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-arabic;
    direction: rtl;
  }
  
  /* Enhanced Arabic text styling */
  .arabic-text {
    font-family: 'Amiri', 'Scheherazade New', serif;
    line-height: 2.5;
    text-align: right;
    direction: rtl;
    font-feature-settings: "liga" 1, "dlig" 1, "kern" 1;
    text-rendering: optimizeLegibility;
  }

  .quran-text {
    font-family: 'Scheherazade New', 'Amiri', serif;
    font-weight: 400;
    line-height: 2.2;
    text-align: right;
    direction: rtl;
    font-feature-settings: "liga" 1, "dlig" 1, "kern" 1, "mark" 1, "mkmk" 1;
    text-rendering: optimizeLegibility;
    letter-spacing: 0.02em;
  }

  .verse-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background-color: #374151;
    border-radius: 50%;
    font-size: 0.875rem;
    margin: 0 0.5rem;
    font-family: 'Inter', sans-serif;
  }

  /* Custom scrollbar for better UX */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #1f2937;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: #10b981;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #059669;
  }

  /* Smooth transitions */
  * {
    transition: all 0.2s ease-in-out;
  }

  /* Enhanced button hover effects */
  .btn-hover {
    transform: translateY(0);
    transition: all 0.2s ease-in-out;
  }

  .btn-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  }

  /* Loading animation */
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  /* Fade in animation */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  /* Slider styling */
  .slider {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
  }

  .slider::-webkit-slider-track {
    background: #374151;
    height: 8px;
    border-radius: 4px;
  }

  .slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: #10b981;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .slider::-webkit-slider-thumb:hover {
    background: #059669;
    transform: scale(1.1);
  }

  /* Mushaf specific styles */
  .mushaf-page {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border: 2px solid #374151;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  }

  .mushaf-lines {
    text-align: justify;
    text-justify: inter-word;
    hyphens: auto;
    word-spacing: 0.1em;
  }

  .verse-text {
    display: inline;
    padding: 2px 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .verse-text:hover {
    background-color: rgba(75, 85, 99, 0.3);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .verse-card {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    border: 1px solid #4b5563;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .verse-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.3);
  }

  /* Ornate number styling */
  .ornate-number {
    position: relative;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
  }

  .ornate-number::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    border-radius: 50%;
    z-index: -1;
    opacity: 0.5;
  }

  /* Enhanced Surah Header Design - Traditional Islamic Style */
  .surah-header {
    position: relative;
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #1d4ed8 100%);
    border: 3px solid #fbbf24;
    border-radius: 20px;
    padding: 16px 32px;
    margin: 24px auto;
    box-shadow:
      0 8px 32px rgba(251, 191, 36, 0.3),
      inset 0 2px 4px rgba(255, 255, 255, 0.1);
    overflow: hidden;
  }

  .surah-header::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706, #fbbf24);
    border-radius: 22px;
    z-index: -1;
    animation: borderGlow 3s ease-in-out infinite alternate;
  }

  .surah-header::after {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 8px;
    border: 1px solid rgba(251, 191, 36, 0.3);
    border-radius: 12px;
    pointer-events: none;
  }

  /* Decorative corners for surah header */
  .surah-header-corner {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid #fbbf24;
  }

  .surah-header-corner.top-left {
    top: 4px;
    left: 4px;
    border-right: none;
    border-bottom: none;
    border-top-left-radius: 8px;
  }

  .surah-header-corner.top-right {
    top: 4px;
    right: 4px;
    border-left: none;
    border-bottom: none;
    border-top-right-radius: 8px;
  }

  .surah-header-corner.bottom-left {
    bottom: 4px;
    left: 4px;
    border-right: none;
    border-top: none;
    border-bottom-left-radius: 8px;
  }

  .surah-header-corner.bottom-right {
    bottom: 4px;
    right: 4px;
    border-left: none;
    border-top: none;
    border-bottom-right-radius: 8px;
  }

  /* Enhanced Bismillah Design */
  .bismillah-container {
    position: relative;
    background: linear-gradient(135deg, #065f46 0%, #047857 50%, #059669 100%);
    border: 2px solid #10b981;
    border-radius: 16px;
    padding: 20px;
    margin: 20px auto;
    box-shadow:
      0 6px 20px rgba(16, 185, 129, 0.3),
      inset 0 1px 3px rgba(255, 255, 255, 0.1);
  }

  .bismillah-container::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, #10b981, #059669, #047857, #10b981);
    border-radius: 17px;
    z-index: -1;
    animation: borderGlow 4s ease-in-out infinite alternate;
  }

  /* Traditional Islamic Verse Numbers */
  .verse-number-traditional {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
    border: 2px solid #92400e;
    border-radius: 50%;
    color: #1f2937;
    font-weight: bold;
    font-size: 14px;
    margin: 0 8px;
    box-shadow:
      0 4px 12px rgba(251, 191, 36, 0.4),
      inset 0 1px 2px rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
  }

  .verse-number-traditional::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706, #fbbf24);
    border-radius: 50%;
    z-index: -1;
    opacity: 0.7;
    animation: verseNumberGlow 2s ease-in-out infinite alternate;
  }

  .verse-number-traditional::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    border: 1px solid rgba(146, 64, 14, 0.3);
    border-radius: 50%;
    pointer-events: none;
  }

  /* Ornate border for surah headers */
  .ornate-border {
    position: relative;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 8px 32px rgba(245, 158, 11, 0.4);
  }

  .ornate-border::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(135deg, #fbbf24, #f59e0b, #d97706);
    border-radius: inherit;
    z-index: -1;
    opacity: 0.7;
  }

  /* Enhanced animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes borderGlow {
    0% {
      opacity: 0.5;
      transform: scale(1);
    }
    100% {
      opacity: 0.8;
      transform: scale(1.02);
    }
  }

  @keyframes verseNumberGlow {
    0% {
      opacity: 0.6;
      box-shadow: 0 4px 12px rgba(251, 191, 36, 0.4);
    }
    100% {
      opacity: 0.9;
      box-shadow: 0 6px 20px rgba(251, 191, 36, 0.6);
    }
  }

  @keyframes surahSeparator {
    0% {
      opacity: 0;
      transform: scaleX(0);
    }
    100% {
      opacity: 1;
      transform: scaleX(1);
    }
  }

  .fade-in {
    animation: fadeInUp 0.6s ease-out;
  }

  /* Surah Separator Design */
  .surah-separator {
    position: relative;
    height: 4px;
    background: linear-gradient(90deg, transparent 0%, #fbbf24 20%, #f59e0b 50%, #fbbf24 80%, transparent 100%);
    margin: 32px auto;
    border-radius: 2px;
    animation: surahSeparator 1s ease-out;
  }

  .surah-separator::before {
    content: '۞';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #1f2937;
    color: #fbbf24;
    font-size: 24px;
    padding: 8px 12px;
    border-radius: 50%;
    border: 2px solid #fbbf24;
    box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
  }

  /* Enhanced verse text styling */
  .verse-text-enhanced {
    transition: all 0.3s ease;
    padding: 2px 4px;
    border-radius: 6px;
    position: relative;
  }

  .verse-text-enhanced:hover {
    background-color: rgba(16, 185, 129, 0.1);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
    transform: translateY(-1px);
  }

  /* Bookmarked verse styling */
  .bookmarked-verse {
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.5) !important;
    animation: bookmarkPulse 2s ease-in-out infinite alternate;
  }

  @keyframes bookmarkPulse {
    0% {
      box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
    }
    100% {
      box-shadow: 0 0 25px rgba(255, 255, 255, 0.8);
    }
  }

  /* Enhanced mushaf page styling */
  .mushaf-page-enhanced {
    background:
      radial-gradient(circle at 20% 20%, rgba(251, 191, 36, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
      linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border: 3px solid transparent;
    background-clip: padding-box;
    position: relative;
  }

  .mushaf-page-enhanced::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(45deg, #fbbf24, #10b981, #3b82f6, #fbbf24);
    border-radius: inherit;
    z-index: -1;
    opacity: 0.3;
  }

  /* Traditional Islamic patterns */
  .islamic-pattern {
    background-image:
      radial-gradient(circle at 25% 25%, rgba(251, 191, 36, 0.1) 0%, transparent 25%),
      radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.1) 0%, transparent 25%);
    background-size: 40px 40px;
  }

  /* Responsive text alignment */
  @media (max-width: 768px) {
    .mushaf-lines {
      text-align: right;
    }

    .surah-header {
      padding: 12px 24px;
      margin: 16px auto;
    }

    .bismillah-container {
      padding: 16px;
      margin: 16px auto;
    }

    .verse-number-traditional {
      width: 28px;
      height: 28px;
      font-size: 12px;
      margin: 0 6px;
    }
  }
}
