# 🕌 دليل النظام الهجين للمصحف

## 🎯 ما هو النظام الهجين؟

النظام الهجين يجمع بين **جمال المصحف الأصلي** و **وظائف التطبيق الحديث**:

- ✅ **الشكل**: صور المصحف الأصلي بالخط العثماني
- ✅ **الوظائف**: مشاركة النص، البحث، العلامات، التحديد
- ✅ **التفاعل**: النقر على الآيات للحصول على خيارات
- ✅ **إمكانية الوصول**: يعمل مع قارئ الشاشة

## 🚀 كيفية الوصول للنظام الهجين

1. افتح التطبيق: http://localhost:8080
2. اضغط على **"النظام الهجين الجديد"** في الصفحة الرئيسية
3. أو اذهب مباشرة إلى: http://localhost:8080/hybrid

## 📸 إضافة صور المصحف

### الخطوة 1: الحصول على الصور

#### المصادر الرسمية الموصى بها:

1. **مجمع الملك فهد لطباعة المصحف الشريف**
   - الموقع: https://qurancomplex.gov.sa/
   - صور عالية الجودة ومعتمدة رسمياً

2. **قرآن فلاش**
   - الموقع: https://app.quranflash.com/
   - مصحف المدينة النبوية - 624 صفحة
   - جودة ممتازة ومجاني

3. **مصحف المدينة للنشر الحاسوبي**
   - الموقع: https://nashr.qurancomplex.gov.sa
   - برنامج رسمي من مجمع الملك فهد

### الخطوة 2: تنظيم الصور

```
public/
└── images/
    ├── page_1.jpg
    ├── page_2.jpg
    ├── page_3.jpg
    ...
    └── page_604.jpg
```

#### متطلبات الصور:
- **التنسيق**: JPG أو PNG
- **الجودة**: عالية (على الأقل 1200x1600 بكسل)
- **التسمية**: `page_[رقم الصفحة].jpg`
- **الحجم**: محسن للويب (أقل من 500KB لكل صورة)

### الخطوة 3: تحسين الصور

```bash
# تحسين الصور باستخدام ImageMagick
for i in {1..604}; do
  convert "page_$i.jpg" -quality 85 -resize 1200x1600 "optimized/page_$i.jpg"
done
```

## 🔧 كيفية عمل النظام

### البنية التقنية:

```
HybridQuranPage
├── طبقة الصورة (المصحف الأصلي)
├── طبقة النص التفاعلية (خفية)
└── واجهة التحكم (البحث، العلامات، المشاركة)
```

### تدفق البيانات:

1. **التحميل**: تحويل البيانات الحالية إلى تنسيق هجين
2. **العرض**: عرض الصورة مع طبقة نص تفاعلية
3. **التفاعل**: النقر على الآيات يفعل الوظائف
4. **المشاركة**: نسخ النص الأصلي (ليس الصورة)

## 🎮 الوظائف المتاحة

### 🔍 البحث
- البحث في النص الكامل
- تمييز النتائج على الصفحة
- الانتقال السريع للنتائج

### 📤 المشاركة
- نسخ نص الآية
- مشاركة عبر المتصفح
- تصدير النص بتنسيقات مختلفة

### 🔖 العلامات
- 5 ألوان مختلفة للعلامات
- حفظ العلامات محلياً
- عرض قائمة العلامات المحفوظة

### 🎯 التحديد
- تحديد آيات فردية
- تحديد نطاقات من الآيات
- خيارات متقدمة للتحديد

## 🛠️ التخصيص والتطوير

### إضافة وضع جديد:

```typescript
// في HybridQuranPage.tsx
const [viewMode, setViewMode] = useState<'image' | 'text' | 'overlay'>('image');
```

### تحسين مواقع الآيات:

```typescript
// في hybridDataConverter.ts
function calculateVersePosition(/* parameters */) {
  // خوارزمية محسنة لحساب مواقع الآيات
  return { x, y, width, height };
}
```

### إضافة ميزات جديدة:

```typescript
// مثال: إضافة ميزة التفسير
interface HybridVerse {
  // ... الخصائص الموجودة
  tafsir?: string;
  translation?: string;
}
```

## 📱 الاستخدام على الأجهزة المختلفة

### الهاتف المحمول:
- تصميم متجاوب
- إيماءات اللمس
- تكبير وتصغير الصور

### الحاسوب:
- اختصارات لوحة المفاتيح
- نقر بالماوس
- عجلة التمرير للتنقل

### الأجهزة اللوحية:
- واجهة محسنة للشاشات الكبيرة
- إيماءات متقدمة
- وضع القراءة المريح

## 🔧 استكشاف الأخطاء

### مشكلة: الصور لا تظهر
```
الحل:
1. تأكد من وجود الصور في مجلد public/images/
2. تحقق من أسماء الملفات (page_1.jpg, page_2.jpg, ...)
3. تأكد من صحة المسارات في الكود
```

### مشكلة: النص لا يتطابق مع الصورة
```
الحل:
1. راجع ملف hybridDataConverter.ts
2. اضبط دالة calculateVersePosition
3. قارن مع المصحف الأصلي يدوياً
```

### مشكلة: بطء في التحميل
```
الحل:
1. حسن حجم الصور (أقل من 500KB)
2. استخدم تنسيق WebP
3. فعل التحميل التدريجي (lazy loading)
```

## 📈 التطوير المستقبلي

### الميزات المخططة:
- [ ] تحميل الصور تلقائياً من المصادر الرسمية
- [ ] تحسين خوارزمية مواقع الآيات
- [ ] إضافة التفسير والترجمة
- [ ] وضع القراءة الليلي للصور
- [ ] مزامنة العلامات عبر الأجهزة

### التحسينات التقنية:
- [ ] تحسين الأداء
- [ ] تقليل استهلاك الذاكرة
- [ ] دعم أفضل للأجهزة المحمولة
- [ ] إضافة اختبارات تلقائية

## 🤝 المساهمة

لتحسين النظام الهجين:

1. **اختبر** الوظائف على أجهزة مختلفة
2. **أبلغ** عن أي أخطاء أو مشاكل
3. **اقترح** ميزات جديدة
4. **شارك** في تحسين دقة مواقع الآيات

## 📞 الدعم

للحصول على المساعدة:
- راجع هذا الدليل أولاً
- تحقق من ملفات السجل في وحدة تحكم المتصفح
- اختبر على متصفحات مختلفة

---

**🎉 النظام الهجين جاهز للاستخدام!**

يجمع بين جمال المصحف الأصلي ووظائف التطبيق الحديث، مما يوفر تجربة قراءة مثالية تحترم التراث وتستفيد من التكنولوجيا.
